@tailwind base;
@tailwind components;
@tailwind utilities;

.flexCenter {
  @apply flex justify-center items-center;
}

.ant-input::placeholder {
  color: #006eb8;
}

/* Back To Top Button Start */
.back-to-top-button.visible {
  opacity: 1;
}

/* Back To Top Button End */

.container {
  width: 100%;
  padding-left: 80px;
  padding-right: 80px;
}

/*  Paginations styling  start*/
.ant-pagination .ant-pagination-next .ant-pagination-item-link {
  color: #1a75cf !important;
}

.ant-pagination .ant-pagination-prev .ant-pagination-item-link {
  color: #1a75cf !important;
}

.ant-pagination .ant-pagination-item a:hover {
  color: #012a4d !important;
  font-weight: 600;
}

.ant-pagination .ant-pagination-item-active {
  border-bottom: 2px solid #012a4d;
  border-top: 2px solid white;
  border-right: 2px solid white;
  border-left: 2px solid white;
  border-radius: 0px !important;
  color: #012a4d !important;
  font-weight: 600 !important;
}

.menu-sidebar .ant-menu-title-content {
  max-width: 9rem !important;
  font-size: 12px !important;
  line-height: 14px;
}

.menu-sidebar .ant-menu-title-content a {
  white-space: pre-line;
}

.menu-sidebar .ant-menu-submenu .ant-menu-submenu-title .ant-menu-title-content div {
  white-space: pre-line;
}

@media screen and (min-width: 960px) {
  .menu-sidebar .ant-menu-title-content {
    max-width: 11rem !important;
  }
}

@media screen and (min-width: 1050px) {
  .menu-sidebar .ant-menu-title-content {
    max-width: 14rem !important;
  }
}

@media screen and (min-width: 1280px) {
  .menu-sidebar .ant-menu-title-content {
    max-width: 16rem !important;
  }
}

.ant-dropdown-menu-title-content {
  /* overflow-wrap: break-word; */
  word-break: break-word;
}

.ant-pagination .ant-pagination-item-active:hover {
  border-left-color: white !important;
  border-right-color: white !important;
  border-top-color: white !important;
  border-bottom-color: #012a4d !important;
}

/*  Paginations styling  end*/

/* This is for line ellipses */
.lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  box-orient: vertical;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

/*  slider slike  */

.slick-prev:before {
  color: black !important;
  display: none !important;
}

.slick-next:before {
  color: black !important;
  display: none !important;
}

.slider_arrows .slick-prev:before {
  color: black !important;
  font-size: 30px !important;
  z-index: 1 !important;
}

.slider_arrows .slick-next:before {
  color: black !important;
  font-size: 30px !important;
  z-index: 9999 !important;
}

.slick-arrow slick-prev {
  z-index: 1 !important;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
  margin: 1px;
}

slick-slide slick-active slick-cloned {
  width: 200px !important;
}

/* product Gallery modal ui setting* /




/* :root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}


body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
} */
/* ///NavMenu */

.ant-menu-title-content {
  font-size: 16px;
  font-weight: 600;
  color: #012a4d;
  padding: 0 !important;
}

.ant-menu-item::after {
  display: none !important;
}

.ant-menu-horizontal .ant-menu-item {
  padding-inline: 13px !important;
}

.ant-menu-item:hover:after {
  border-bottom: none !important;
  display: none !important;
}

.ant-menu-item-selected::after {
  display: none !important;
}

.ant-menu-item-open::after {
  display: none !important;
  border-bottom: none !important;
}

.ant-menu-sub .product {
  text-align: left !important;
  padding-left: 25px !important;
}

.ant-menu-item-active::after {
  display: none !important;
  border-bottom: none !important;
}

.txtWhite h1 {
  color: white !important;
  font-size: 15px !important;
  font-weight: 700 !important;
}

.txtWhite .ant-menu-title-content {
  color: white !important;
  font-size: 12px !important;
  font-weight: 400 !important;
}

.txtWhite .ant-menu-submenu-arrow {
  color: white !important;
}

.ant-menu-inline.ant-menu-root {
  border-inline: none !important;
}

.ant-menu-submenu-title {
  width: fit-content !important;
}

.menuLeft .ant-menu-submenu-title span {
  font-size: 12px !important;
  font-weight: 700 !important;
}

.menuLeft .ant-menu-item-only-child span {
  font-size: 12px !important;
  font-weight: 700 !important;
}

.menuLeft .product span {
  font-size: 12px !important;
  font-weight: 400 !important;
}

.ant-menu-light .ant-menu-item-selected {
  background: none !important;
}

.ant-menu-submenu-selected::after {
  display: none !important;
  border-bottom: none !important;
}

.ant-menu-light.ant-menu-inline .ant-menu-item-selected span {
  background: white !important;
  color: #012a4d !important;
}

.ant-menu-light.ant-menu-inline .ant-menu-item-selected {
  background: white !important;
}

.ant-menu-submenu-active::after {
  display: none !important;
  border-bottom: none !important;
}

.ant-menu-submenu:hover::after {
  border-bottom: none !important;
  display: none !important;
}

.ant-menu-sub {
  background: none !important;
}

/* //lang Selector */
.ant-select-selector {
  background: #ededed !important;
  border: none !important;
}

.ant-select-selection-item {
  font-weight: 500 !important;
}

/* //drawer */
.ant-drawer-content {
  background: #ededed !important;
}

.ant-drawer-body {
  padding: 0 !important;
}

.ant-drawer-header {
  display: none !important;
}

.flexCenter {
  @apply flex justify-center items-center;
}

.flexBetween {
  @apply flex justify-between items-center;
}

@media screen and (max-width: 737px) {
  .container {
    padding-left: 30px;
    padding-right: 30px;
  }
}

@media screen and (max-width: 500px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }
}

.product_Gallery_Modal.ant-modal .ant-modal-content {
  width: 100% !important;
  max-width: 800px !important;
  height: 100%;
  max-height: 800px !important;
  border-radius: 0px !important;
}

/* ANTD FLOAT BUTTON STYLING START */

.ant-float-btn-default .ant-float-btn-body {
  background-color: #006eb7;
  transition: background-color 0.2s;
}

.ant-float-btn-default .ant-float-btn-body:hover {
  background-color: #006eb7;
  border: 1px solid #012a4d;
}

.ant-float-btn-circle .ant-float-btn-body {
  border-radius: 0%;
}

/* /////Categories SideBar */
@media only screen and (min-width: 768px) {
  .ant-menu-light.ant-menu-inline .ant-menu-item-selected span {
    background: #ededed !important;
  }

  .ant-menu-light.ant-menu-inline .ant-menu-item-selected {
    background: #ededed !important;
  }
}

/* For disabled the iphone zooming */
@media screen and (-webkit-min-device-pixel-ratio: 0) {

  select,
  textarea,
  input {
    font-size: 16px !important;
  }
}

.ant-dropdown-menu .ant-dropdown-menu-submenu-title .ant-dropdown-menu-submenu-expand-icon {
  display: none !important;
}

.ant-dropdown {
  max-width: 200px !important;
}

.ant-dropdown-menu-submenu-placement-rightTop {
  /* display: none !important; */
  max-width: 140px !important;
}

.ant-dropdown .ant-dropdown-menu {
  background: #012a4d !important;
  border-radius: 0px !important;
}

.ant-dropdown-menu-title-content {
  color: #fff !important;
}

.ant-dropdown-menu-submenu .ant-dropdown-menu {
  background: #012a4d !important;
  border-radius: 0px !important;
  color: #fff !important;
}

.ant-dropdown-menu-item:hover,
.ant-dropdown-menu-submenu:hover {
  border-radius: 0px !important;
  background-color: #035094d5 !important;
}

/* globals.css */
.loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  z-index: 9999;
  font-size: 2em;
}

.ant-dropdown-menu .ant-dropdown-menu-sub {
  max-height: 20rem !important;
  overflow-y: auto !important;
  width: 300px;
}

@media only screen and (max-width: 650px) {
  .ant-dropdown-menu .ant-dropdown-menu-sub {
    width: 200px;
  }
}

.image-style-align-right {
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
}

.image-style-align-left {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
}

.image-style-align-center {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

#product-menu [role="menuitem"] span.ant-menu-title-content {
  max-width: unset !important;
}

#product-menu [role="menuitem"] span.ant-menu-title-content a {
  white-space: nowrap;
}

#product-menu ul li a {
  color: #666 !important;
}
