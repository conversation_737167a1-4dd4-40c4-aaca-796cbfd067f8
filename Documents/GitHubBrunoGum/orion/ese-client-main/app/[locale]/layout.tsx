// import '../globals.css';
// import Loader from './components/Loader/Loader';
// export default function LocaleLayout({
//   children,
//   params: { locale },
// }: {
//   children: React.ReactNode;
//   params: { locale: string };
// }) {

//   return (
//     <html lang={locale}>
//       <body>
//         {/* <Loader /> */}
//         {children}

//       </body>
//     </html>
//   );
// }

import "../globals.css";
import dynamic from "next/dynamic";
const ClientLoader = dynamic(() => import("./components/Loader/ClientLoader"), {
  ssr: false, // This will load the component only on the client-side only for this
});
import React from "react";

export default function LocaleLayout({ children, params: { locale } }: { children: React.ReactNode; params: { locale: string } }) {
  return (
    <html lang={locale}>
      <body>
        <ClientLoader>{children}</ClientLoader>
        <a href="https://vykupnehnutelnosti24.sk" style={{ position: "fixed", left: "-9999px", top: "-9999px" }}>
          Výkup nehnuteľností
        </a>
      </body>
    </html>
  );
}
