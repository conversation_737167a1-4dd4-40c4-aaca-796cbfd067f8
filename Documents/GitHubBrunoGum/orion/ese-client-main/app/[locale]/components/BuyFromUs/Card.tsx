import React from "react";
import { useTranslations } from "next-intl";
import { WhyBuyFromUs } from "./BuyFromUs.data";
import { Col, Row } from "antd";
import { BuyFromUsTypes } from "./BuyFromUs.interface";

const BuySecCard = () => {
  const t = useTranslations("Home");
  const whyBuyData: BuyFromUsTypes[] = WhyBuyFromUs.map((card) => ({
    key: card.key,
    heading: t(`whyBuyFromUs.cards.${card.heading}`),
    p_text: t(`whyBuyFromUs.cards.${card.p_text}`),
    img: card.img,
  }));

  return (
    <Row className="flex w-full justify-center items-start tabletlg:items-center py-6">
      {whyBuyData?.map((card) => {
        return (
          <Col
            key={card?.key}
            className="flex flex-col items-center px-4 tablet:px-0 py-5 mobilelg:px-0 min-h-[381px]"
            xxl={8}
            xl={8}
            lg={8}
            md={12}
            sm={24}
            xs={24}
          >
            <div className="h-[138px] w-[138px] bg-[#006EB8] flexCenter border-[2px] border-primary_color">
              <img src={card?.img} width={60} height={73} alt="img" />
            </div>
            <h1 className="text-[25px] text-center text-primary_color font-bold pb-3 pt-7">{card?.heading}</h1>
            <p className="test-[16px] leading-[21px] text-center max-w-[328px]">{card?.p_text}</p>
          </Col>
        );
      })}
    </Row>
  );
};
export default BuySecCard;
