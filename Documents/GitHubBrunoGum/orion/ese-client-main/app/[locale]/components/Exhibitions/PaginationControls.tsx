'use client';
import React from 'react';
import { Pagination } from 'antd';
import { useRouter } from 'next/navigation';

const PaginationControls = ({
  total,
  locale,
  pageSize,
  currentPage,
}: {
  total: number;
  locale: string;
  pageSize: number;
  currentPage: number;
}) => {
  const router = useRouter();
  const handlePagenation = (page: number) => {
    router.push(`/${locale}/exhibitions?&page=${page}`);
  };
  return (
    <div className="flexCenter my-[50px]">
      <Pagination
        defaultCurrent={currentPage / pageSize + 1 || 1}
        total={total}
        pageSize={pageSize}
        onChange={handlePagenation}
      />
    </div>
  );
};
export default PaginationControls;
