import { Col, Row } from "antd";
import Paragraph from "../Shared/Paragraph/Paragraph";
import React from "react";
import PaginationControls from "./PaginationControls";
import dayjs from "dayjs";

async function getData({ skip, take }: { skip: number; take: number }) {
  const res = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/messe?&skip=${skip}&take=${take}`, {
    // next: { revalidate: 1 },
    cache: "no-cache",
  });

  if (!res.ok) {
    throw new Error("Failed to fetch data");
  }
  return res.json();
}

async function Exhibititions({ page, locale }: { page: number; locale: string }) {
  const take = 2;
  const skip = (Number(page) - 1) * take;
  const MesseData = await getData({ skip, take });
  const Manuals = MesseData.manuals;
  const exhibitionData = Manuals;
  return (
    <>
      {exhibitionData?.map((data: any) => (
        <div className="flexCenter w-full " key={data.id}>
          <Row gutter={[32, 32]} className="flex justify-between items-start w-full border-2 border-solid border-[#EDEDED] p-[30px] ">
            <Col xs={24} sm={24} md={24} lg={10} xl={10} xxl={10} className="w-full hidden tabletlg:block">
              <div className="flex justify-center items-center w-full  h-[681px] mobile:h-[481px]">
                <img
                  className="w-[480px] h-[680px]"
                  src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${data?.File?.url}`}
                  alt="exhibition-button"
                  width={480}
                  height={680}
                />
              </div>
            </Col>
            <Col xs={24} sm={24} md={24} lg={14} xl={14} xxl={14} className="flexCenter items-center w-full flex-col gap-y-[20px]">
              <div className="w-full  text-[36px]  font-[600]">{locale === "en" ? data?.title : locale === "fr" ? data?.titleFr : data?.titleGe}</div>
              <div className="w-full  text-[24px]  font-[500] text-[#006EB8]">
                {dayjs(data?.date).format("DD.MM.YYYY")} | {locale === "en" ? data?.city : locale === "fr" ? data?.cityFr : data?.cityGe}
              </div>
              <Paragraph text={locale === "en" ? data?.description : locale === "fr" ? data?.descriptionFr : data?.descriptionGe} />
            </Col>
            <Col xs={24} sm={24} md={24} lg={10} xl={10} xxl={10} className=" flex justify-end items-center w-full  tabletlg:hidden ">
              <div className="flex justify-end items-center w-full  h-[681px]  ">
                <img
                  className="w-[480px] h-[680px]"
                  src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${data?.File?.url}`}
                  alt="exhibition-button"
                  width={480}
                  height={680}
                />
              </div>
            </Col>
          </Row>
        </div>
      ))}
      <PaginationControls total={MesseData?.count} locale={locale} pageSize={take} currentPage={skip} />
    </>
  );
}

export default Exhibititions;
