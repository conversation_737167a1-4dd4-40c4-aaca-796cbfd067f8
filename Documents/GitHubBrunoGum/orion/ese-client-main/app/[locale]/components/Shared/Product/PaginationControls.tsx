'use client';
import React from 'react';
import { Pagination } from 'antd';
import { useRouter } from 'next/navigation';
interface compTypes {
  total: number;
  categoryId: number | undefined;
  locale: string;
  categPage: number | undefined;
  sidebarPage?: number;
  skip: number;
  take: number;
  subCategoryId?: number;
  bigCategoryId?: number;
}
const PaginationControls = ({
  total,
  categoryId,
  locale,
  categPage,
  sidebarPage,
  skip,
  take,
  subCategoryId,
  bigCategoryId,
}: compTypes) => {
  const router = useRouter();

  const handlePagination = (page: number) => {
    router.push(
      categoryId
        ? `/${locale}/prodbycateg?categoryId=${categoryId}&page=${page}&categPage=${
            categPage || 1
          }&sidebarPage=${sidebarPage || 10}`
        : subCategoryId
        ? `/${locale}/prodbycateg?subCategoryId=${subCategoryId}&page=${page}&categPage=${
            categPage || 1
          }&sidebarPage=${sidebarPage || 10} `
        : bigCategoryId
        ? `/${locale}/prodbycateg?bigCategoryId=${bigCategoryId}&page=${page}&categPage=${
            categPage || 1
          }&sidebarPage=${sidebarPage || 10} `
        : `/${locale}/products?&page=${page}`
    );
  };
  return (
    <div className="flexCenter my-[50px]">
      <Pagination
        defaultCurrent={skip / take + 1 || 1}
        total={total && total}
        pageSize={take}
        onChange={handlePagination}
      />
    </div>
  );
};
export default PaginationControls;
