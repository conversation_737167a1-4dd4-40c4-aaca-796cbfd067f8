import { fileTypes } from '../../ProductDetail/ProductGallery/ProdTypes';

export interface ProdByCategTypes {
  title: string;
  catgTitle: string;
  categoryId: number;
}
export interface FilesTypes {
  id: number;
  url: string;
  mimeType: string;
  productId: number;
  deleted: boolean;
  downloadsId?: number | null;
  createdAt: string;
  updatedAt: string;
}
export interface ProductTypes {
  id: number;
  title: string;
  titleGe: string;
  titleFr: string;
  description: string;
  descriptionGe: string;
  descriptionFr: string;
  categoryId: number;
  deleted: boolean;
  createdAt: string;
  updatedAt: string;
  imgAlt?: string;
  Files: FilesTypes[];
}
export interface categoryTypes {
  id: number;
  name?: string;
  nameGe?: string;
  nameFr?: string;
  title?: string;
  titleGe?: string;
  titleFr?: string;
  SubCategory?: any;
  Product: ProductTypes[];
  File: fileTypes;
}
