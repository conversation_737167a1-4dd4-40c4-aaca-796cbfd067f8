"use client";
import React from "react";
import { Select } from "antd";
import Image from "next/image";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
const { Option } = Select;

const menuStyle = {
  backgroundColor: "#ededed",
  borderRadius: "0",
  boxShadow: "none",
  marginTop: "1.6rem",
};
const Selector = ({ locale }: { locale: string }) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const handleChange = (value: any) => {
    const currentLocale = pathname.split("/")[1];
    const newPathname = pathname.replace(currentLocale, value.toLowerCase());
    const newUrl = `${newPathname}?${searchParams.toString()}`;

    router.replace(newUrl);
  };
  return (
    <Select defaultValue={locale.toUpperCase()} dropdownStyle={menuStyle} onChange={(e) => handleChange(e)} value={locale.toUpperCase()}>
      <Option value="DE">
        <div className="flex">
          <img src={"/assets/icons/GeFlag.svg"} alt="img" width={20} height={20} />
          <span className="text-[14px] text-primary_color px-2">DE</span>
        </div>
      </Option>
      <Option value="EN">
        {" "}
        <div className="flex">
          <img src={"/assets/icons/EngFlag.svg"} alt="img" width={20} height={20} />
          <span className="text-[14px] text-primary_color px-2">ENG</span>
        </div>
      </Option>
      <Option value="FR">
        <div className="flex">
          <img src={"/assets/icons/FrFlag.svg"} alt="img" width={20} height={20} />
          <span className="text-[14px] text-primary_color px-2">FR</span>
        </div>
      </Option>
    </Select>
  );
};

export default Selector;
