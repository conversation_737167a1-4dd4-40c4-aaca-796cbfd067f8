"use client";
// import Image from 'next/image';
import React, { useEffect, useState } from "react";
import { Menu } from "antd";
import { MenuItem } from "./Navbar.interface";
import Link from "next/link";
import { Drawer, Row, Col } from "antd";
import { MenuOutlined } from "@ant-design/icons";
import Tobbar from "../Topbar/Tobbar";
import DrawerMenu from "./DrawerMenu";
import useDebounce from "@/utils/debounce";
import ProductCard from "../../ProductCard/ProductCard";
import dynamic from "next/dynamic";
import { useAppContext } from "../Header/context";

const Selector = dynamic(() => import("./Selector"), {
  ssr: false,
});
interface NavTypes {
  menuItems: MenuItem[];
  tb: string;
  locale: string;
}

async function getData({ search }: { search: string }) {
  const res = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/product/?skip=${0}&search=${search}&take=999999999999999`);
  if (!res.ok) {
    throw new Error("Failed to fetch data");
  }
  return res.json();
}

const Navbar = ({ menuItems, tb, locale }: NavTypes) => {
  // const [drawerVisible, setDrawerVisible] = useState(false);
  const appContext = useAppContext();
  const drawerVisible = appContext?.drawerVisible ?? false;
  const setDrawerVisible = appContext?.setDrawerVisible ?? (() => {});
  const [isOpen, setIsOpen] = useState(false);
  const [searchString, setSearchString] = useState("");
  const debounceSearch = useDebounce(searchString, 800);
  const [searchProduct, setSearchProduct] = useState<any>([]);

  const handleSearchData = async ({ search }: { search: string }) => {
    const searchData = await getData({ search });
    setSearchProduct(searchData?.products);
  };

  useEffect(() => {
    if (debounceSearch) {
      handleSearchData({ search: debounceSearch });
    }
  }, [debounceSearch]);

  const handleSearch = () => {
    setIsOpen(!isOpen);
    if (drawerVisible) {
      setDrawerVisible(false);
    }
  };
  const handleCancel = () => {
    setIsOpen(!isOpen);
    setSearchString("");
    setSearchProduct([]);
    if (drawerVisible) {
      setDrawerVisible(false);
    }
  };
  const toggleDrawer = () => {
    setDrawerVisible(!drawerVisible);
  };

  const onSearch = (value: string) => {
    setSearchString(value);
    if (value === "") {
      setSearchProduct([]);
    }
  };

  const handleMenuItemClick = () => {
    // if (info.key !== '2') {
    setDrawerVisible(false);
    // }
  };

  return (
    <nav className="w-full px-12 mobilelg:px-5 py-5 bg-[#ededed]">
      {!isOpen ? (
        <Row className="w-full flex justify-between tabletlg:items-center">
          <Col xxl={4} xl={5} lg={4} md={5} sm={6} xs={12}>
            <Link href={`/${locale}`}>
              <img src={"/assets/images/logo.png"} alt="img" width={114} height={80} />
            </Link>
          </Col>
          <Col className="block tabletlg:hidden" xxl={12} xl={12} lg={14} md={14} sm={0} xs={0}>
            <div>
              <Menu mode="horizontal" className="flex flex-wrap bg-transparent border-0 uppercase" items={menuItems} disabledOverflow />
            </div>
          </Col>
          <Col className="flex justify-around pl-6 items-center h-full" xxl={8} xl={7} lg={6} md={5} sm={6} xs={12}>
            <div className="block tabletlg:hidden">
              <Selector locale={locale} />
            </div>

            <div className="flex justify-between mobilelg:w-full mobilelg:justify-end">
              <div>
                <div className="hidden tabletlg:block">
                  <Drawer title="Drawer" placement="left" open={drawerVisible} onClose={toggleDrawer}>
                    <div className="flexBetween w-full p-4">
                      <div>
                        <img src={"/assets/images/logo.png"} alt="img" width={114} height={80} />
                      </div>
                      <div className="flex text-[18px] text-primary_color gap-x-5">
                        <div onClick={handleSearch} className="cursor-pointer">
                          <img src={"/assets/icons/search.svg"} alt="img" className="!text-[16px]" width={18} height={18} />
                        </div>
                        <MenuOutlined onClick={toggleDrawer} />
                      </div>
                    </div>
                    <DrawerMenu>
                      <Menu
                        mode="vertical"
                        className="flex flex-wrap bg-transparent border-0"
                        items={menuItems}
                        onClick={handleMenuItemClick}
                        disabledOverflow
                      />
                    </DrawerMenu>

                    <div className="p-4">
                      <Selector locale={locale} />
                    </div>
                    <Tobbar flexCol={true} py={true} t={tb} />
                  </Drawer>
                </div>

                <div className="flexCenter text-[18px] text-primary_color mobilelg:gap-x-5 fill-fit">
                  <div onClick={handleSearch} className="cursor-pointer">
                    <img src={"/assets/icons/search.svg"} className="!text-[16px]" alt="img" width={18} height={18} />
                  </div>
                  <MenuOutlined onClick={toggleDrawer} className="hidden tabletlg:block mobilelg:pl-0 pl-10 " />
                </div>
              </div>
            </div>
          </Col>
        </Row>
      ) : (
        <div className="flex items-center w-full py-[6px] tabletlg:!py-[14px] bg-[#ededed] ">
          <Row className="w-full items-center">
            <Col xxl={6} xl={6} lg={6} md={0} sm={0} xs={0}>
              <Link href={"/de/"}>
                <img src={"/assets/images/logo.png"} alt="img" width={114} height={80} />
              </Link>
            </Col>
            <Col xxl={12} xl={12} lg={12} md={24} sm={24} xs={24} className="tabletlg:px-12 mobilelg:!px-0">
              <div className="w-full relative">
                <div className="bg-white flex h-fit justify-between items-center p-3">
                  <div className="search-icon px-2 pr-5 w-[20px] h-[20px] relative object-contain">
                    <img src={"/assets/icons/search.svg"} alt="Search" className="object-contain !text-[16px]" />
                  </div>

                  <input
                    onChange={(e) => onSearch(e.target.value)}
                    type="text"
                    placeholder="Search..."
                    className="ouline-transparent w-full border-0 focus:outline-none focus:border-transparent"
                    autoFocus
                  />
                  <button onClick={handleCancel} className="pr-2 text-lg pl-5 text-primary_color font-semibold">
                    &#10005;
                  </button>
                </div>
                {searchProduct?.length > 0 && (
                  <div
                    className="flex justify-center mobile:flex-col flex-wrap items-center bg-[#EDEDED]  w-full absolute z-50 pt-5 max-h-[600px] overflow-y-auto"
                    style={{ zIndex: "1000" }}
                  >
                    {searchProduct?.length > 0 &&
                      searchProduct?.map((item: any) => {
                        return (
                          <div key={item?.id} className="flex justify-center items-center h-full w-fit" onClick={handleCancel}>
                            <Link href={`/${locale}/product-detail?productId=${item?.id}`} className="h-full w-full">
                              <ProductCard
                                HideBtn={true}
                                color={"#EDEDED"}
                                underline={true}
                                imgSize={true}
                                imgAlt={item.imgAlt}
                                title={`${locale === "en" ? item?.title : locale === "de" ? item?.titleGe : locale === "fr" ? item?.titleFr : ""}`}
                                description={`${
                                  locale === "en" ? item?.description : locale === "de" ? item?.descriptionGe : locale === "fr" ? item?.descriptionFr : ""
                                }`}
                                imgUrl={item?.Files && item?.Files[0]?.url}
                              />
                            </Link>
                          </div>
                        );
                      })}
                  </div>
                )}
              </div>
            </Col>
          </Row>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
