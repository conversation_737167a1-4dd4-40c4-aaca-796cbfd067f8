// @ts-nocheck
import React, { useEffect, useState } from 'react';
import Sidebar from './Sidebar';
import { Col, Empty, Pagination, Row } from 'antd';
import DrawerMenu from '../Layout/Navbar/DrawerMenu';
import Link from 'next/link';
import ProductCard from '../ProductCard/ProductCard';
import SidebarMenu from './SidebarMenu';
import { ProductTypes } from './Product.interface';
import PaginationControls from './PaginationControls';
interface compTypes {
  title: string;
  catgTitle?: string | undefined;
  categoryId?: number | undefined;
  page: number;
  locale: string;
  categPage?: number | undefined;
  sidebarPage?: number;
  subCategoryId?: number;
  bigCategoryId?: number;
}
async function getData({
  categoryId,
  subCategoryId,
  bigCategoryId,
}: {
  categoryId: any;
  subCategoryId?: number;
  bigCategoryId?: number;
}) {
  const res = await fetch(
    categoryId || subCategoryId || bigCategoryId
      ? `${process.env.NEXT_PUBLIC_BACKEND_URL}/product/by-Category?categoryId=${categoryId}&subCategoryId=${subCategoryId}&bigCategoryId=${bigCategoryId}&take=99999999`
      : `${process.env.NEXT_PUBLIC_BACKEND_URL}/product?take=99999999`,
    {
      // next: { revalidate: 1 },
      cache: 'no-cache',
    }
  );
  if (!res.ok) {
    throw new Error('Failed to fetch data');
  }
  return res.json();
}

const ProductItem = ({ locale, product }) => {
  return <DrawerMenu>
    <Link href={`/${locale}/product-detail/${product?.id}`}>
      <ProductCard
        HideBtn={true}
        underline={true}
        imgAlt={product.imgAlt}
        title={`${locale === 'en'
          ? product?.title
          : locale === 'de'
            ? product?.titleGe
            : locale === 'fr'
              ? product?.titleFr
              : ''
          }`}
        description={`${locale === 'en'
          ? product?.title
          : locale === 'de'
            ? product?.titleGe
            : locale === 'fr'
              ? product?.titleFr
              : ''
          }`}
        btnText=""
        imgUrl={product?.Files.sort((a: any, b: any) => a.id - b.id)[0]?.url}
      />
    </Link>
  </DrawerMenu>
}

const ProductByCat = async ({
  title,
  catgTitle,
  categoryId,
  page,
  locale,
  categPage,
  sidebarPage,
  subCategoryId,
  bigCategoryId,
}: compTypes) => {

  const take = -1;
  const skip = (Number(page) - 1) * take;

  const data = await getData({
    categoryId,
    subCategoryId,
    bigCategoryId,
  });

  const Products =
    categoryId || subCategoryId || bigCategoryId
      ? data?.existCategory
      : data?.products

  const renderProducts = () => {
    if (Products.length < 3) {
      return <div className='flex flex-row items-center justify-center gap-8'>
        {Products?.map((product: ProductTypes) => (
          <ProductItem key={product.id} product={product} locale={locale} />
        ))}
      </div>
    }

    return <Row gutter={[10, 2]}>
      {Products?.map((product: ProductTypes) => (
        <Col
          className="flexCenter pt-7 pb-9"
          key={product?.id}
          xxl={8}
          xl={8}
          lg={8}
          md={12}
          sm={12}
          xs={12}
        >
          <ProductItem product={product} locale={locale} />
        </Col>
      ))}
    </Row>
  };

  return (
    <>
      <h1 className="text-[60px] mobile:text-[30px] text-center font-bold text-gray-800 py-9">
        {title}
      </h1>
      <div className="hidden tablet:flex justify-center pb-4 border-2 bg-[#1A75CF] txtWhite pt-3">
        <Sidebar catgTitle={catgTitle}>
          <SidebarMenu locale={locale} />
        </Sidebar>
      </div>
      {Products?.length > 0 ? renderProducts() : (
        <Empty description="No Products" />
      )}
      {/* <div className="w-full flexCenter tablet:px-0 pr-12"> */}
      {/*   <PaginationControls */}
      {/*     total={data && data?.count} */}
      {/*     categoryId={categoryId} */}
      {/*     subCategoryId={subCategoryId} */}
      {/*     bigCategoryId={bigCategoryId} */}
      {/*     locale={locale} */}
      {/*     categPage={categPage} */}
      {/*     sidebarPage={sidebarPage} */}
      {/*     skip={skip} */}
      {/*     take={take} */}
      {/*   /> */}
      {/* </div> */}
    </>
  );
};

export default ProductByCat;
