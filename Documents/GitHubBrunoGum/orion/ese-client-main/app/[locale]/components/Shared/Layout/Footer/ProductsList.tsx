import Link from "next/link";
import React from "react";
import { categoryTypes } from "../../Product/Product.interface";

async function getData() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/category/big-category`, {
    // next: { revalidate: 1 },
    cache: "no-cache",
  });

  if (!res.ok) {
    throw new Error("Failed to fetch data");
  }

  return res.json();
}
//
const ProductsList = async ({ locale }: { locale: string }) => {
  const data = await getData();
  return (
    <div>
      {data?.categories?.map((category: categoryTypes) => {
        return (
          <ul key={category?.id} className="list-none pl-0">
            <li className="py-[.5rem] text-[14px] uppercase">
              <Link href={`/${locale}/prodbycateg?bigCategoryId=${category?.id}`} className="flex gap-x-2">
                <div className="max-[20px] max-h-[20px]">
                  <img
                    src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${category?.File?.url}`}
                    width="20"
                    height="20"
                    alt="img"
                    className="w-[20px] h-[20px] rounded-[50%]"
                  />
                </div>
                {`${locale === "en" ? category?.name : locale === "de" ? category?.nameGe : locale === "fr" ? category?.nameFr : ""}`}
              </Link>
            </li>
          </ul>
        );
      })}
    </div>
  );
};

export default ProductsList;
