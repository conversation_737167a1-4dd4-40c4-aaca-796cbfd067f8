"use client";
import Link from "next/link";
import React from "react";
import ContactNumber from "../../../ContactNumber";
import { useAppContext } from "../Header/context";

interface TopbarProps {
  flexCol?: boolean;
  py?: boolean;
  colReverse?: boolean;
  t: string;
}

function Tobbar({ flexCol = false, py = false, colReverse = false, t }: TopbarProps) {
  const appC = useAppContext();
  return (
    <div className={`w-full flex  ${!flexCol ? "px-10" : ""} items-center bg-primary_color text-white ${!colReverse && flexCol ? "p-4" : ""}`}>
      <div className={` w-fit flex ${flexCol ? "flex-col" : ""} ${colReverse ? "flex-col-reverse" : ""}`}>
        <div className={`${py ? "py-4" : ""} flex items-center ${!colReverse ? "p-2 pr-12" : "py-3"}`}>
          {!colReverse && (
            <a href={`tel:${appC?.contact?.number}`} className="mr-5 cursor-pointer">
              <img src={"/assets/icons/call.svg"} alt="img" width={20} height={19} />
            </a>
          )}
          <ContactNumber />
        </div>
        <div className={`${py ? "py-4" : ""} flex items-center ${!colReverse ? "p-2 pr-12" : "py-3"} `}>
          {!colReverse && (
            <a href="mailto:<EMAIL>" className="mr-5 cursor-pointer">
              <img src={"/assets/icons/mail.svg"} alt="img" width={20} height={20} />
            </a>
          )}
          <a href="mailto:<EMAIL>" className="text-[14px] cursor-pointer">
            <EMAIL>
          </a>
        </div>
        <div className={`${py ? "py-4" : ""} flex items-center ${!colReverse ? "p-2 pr-12" : "py-3"} `}>
          {!colReverse && (
            <span className="mr-5 cursor-pointer">
              <img src={"/assets/icons/distance.svg"} alt="img" width={12} height={22} />
            </span>
          )}
          <Link
            href={
              "https://www.google.com/maps/place/Donatusstr.+26A,+50767+Köln,+Germany/@51.0067568,6.8673614,17z/data=!3m1!4b1!4m6!3m5!1s0x47bf304523d5b2d7:0x1fd3934f6bd88ae6!8m2!3d51.0067568!4d6.8699363!16s%2Fg%2F11bw421htz?entry=ttu"
            }
            target="-blank"
            className="text-[14px]"
          >
            {t}
          </Link>
        </div>
      </div>
    </div>
  );
}

export default Tobbar;
