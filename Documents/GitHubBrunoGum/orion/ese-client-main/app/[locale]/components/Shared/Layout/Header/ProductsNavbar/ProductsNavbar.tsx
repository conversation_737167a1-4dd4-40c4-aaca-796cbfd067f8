/* eslint-disable @next/next/no-async-client-component */
'use client';
import { Button, Dropdown, Menu, MenuProps } from 'antd';
import React, { useEffect, useState } from 'react';
import { DownOutlined, RightOutlined } from '@ant-design/icons';
import Link from 'next/link';
import { useAppContext } from '../context';

async function getData() {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/category/product-category`,
    {
      // next: { revalidate: 1 },
      cache: 'no-cache',
    }
  );

  if (!res.ok) {
    throw new Error('Failed to fetch data');
  }

  return res.json();
}
function ProductsNavbar({
  text,
  locale,
  AllProducts,
}: {
  text: any;
  locale: string;
  AllProducts: any;
}) {
  const [data, setData] = useState<any>(null);
  const appContext = useAppContext();
  const drawerVisible = appContext?.drawerVisible ?? false;
  const setDrawerVisible = appContext?.setDrawerVisible ?? (() => {});

  useEffect(() => {
    async function fetchData() {
      try {
        const result = await getData();
        setData(result);
      } catch (error) {
        console.error('Failed to fetch data', error);
      }
    }
    fetchData();
  }, []);

  const processedMenuItems = data?.categoryOrProduct?.map((category: any) => {
    const subcategoryLinks = Array.isArray(category?.SubCategory)
      ? category.SubCategory.map((subcategory: any) => ({
          key: subcategory?.id + Math.random(),
          label: (
            <Link
              href={`/${locale}/prodbycateg?subCategoryId=${subcategory?.id}`}
            >
              {`${
                locale === 'en'
                  ? subcategory?.name
                  : locale === 'de'
                  ? subcategory?.nameGe
                  : locale === 'fr'
                  ? subcategory?.nameFr
                  : ''
              }`}
            </Link>
          ),
        }))
      : [];

    const productLinks = Array.isArray(category?.Product)
      ? category.Product.map((product: any) => ({
          key: product?.id + Math.random(),
          label: (
            <Link href={`/${locale}/product-detail?productId=${product?.id}`}>
              {`${
                locale === 'en'
                  ? product?.title
                  : locale === 'de'
                  ? product?.titleGe
                  : locale === 'fr'
                  ? product?.titleFr
                  : ''
              }`}
            </Link>
          ),
        }))
      : [];

    const children = [...subcategoryLinks, ...productLinks];

    return {
      key: category?.id + Math.random(),
      label: (
        <span>
          {category?.Product ? (
            category?.Product?.length > 0 ||
            category?.SubCategory?.length > 0 ? (
              <span>
                {`${
                  locale === 'en'
                    ? category?.name
                    : locale === 'de'
                    ? category?.nameGe
                    : locale === 'fr'
                    ? category?.nameFr
                    : ''
                }`}
              </span>
            ) : (
              <Link
                href={`/${locale}/prodbycateg?categoryId=${category?.id}`}
                onClick={() => setDrawerVisible(false)}
              >
                {`${
                  locale === 'en'
                    ? category?.name
                    : locale === 'de'
                    ? category?.nameGe
                    : locale === 'fr'
                    ? category?.nameFr
                    : ''
                }`}
              </Link>
            )
          ) : (
            <Link href={`/${locale}/product-detail?productId=${category?.id}`}>
              {`${
                locale === 'en'
                  ? category?.title
                  : locale === 'de'
                  ? category?.titleGe
                  : locale === 'fr'
                  ? category?.titleFr
                  : ''
              }`}
            </Link>
          )}
          {children.length > 0 && <RightOutlined className="pl-1" />}
        </span>
      ),
      children: children.length > 0 ? children : undefined,
    };
  });
  const allProductsButton = (
    <Menu.Item key="all-products">
      <Link href={`/${locale}/products`}>{AllProducts}</Link>
    </Menu.Item>
  );
  //
  return (
    <div className="flex justify-start items-center w-full navbarIcon">
      {processedMenuItems && (
        <Dropdown
          overlay={
            <Menu
              className="h-full max-h-[500px] w-[300px] overflow-x-hidden mobilelg:w-[200px]"
              onClick={() => setDrawerVisible(false)}
            >
              {allProductsButton}
              {processedMenuItems.map((item: any) => (
                <Menu.SubMenu key={item.key} title={item.label}>
                  {item.children &&
                    item.children.map((child: any) => (
                      <Menu.Item key={child.key}>{child.label}</Menu.Item>
                    ))}
                </Menu.SubMenu>
              ))}
            </Menu>
          }
          className="navbarIcon"
          placement="bottomCenter"
        >
          <div className="flex justify-center items-center cursor-pointer gap-x-[10px]">
            {text}
            <div>
              <DownOutlined />
            </div>
          </div>
        </Dropdown>
      )}
    </div>
  );
}

export default ProductsNavbar;
