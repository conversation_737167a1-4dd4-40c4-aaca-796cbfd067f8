import React from "react";
import Tobbar from "../Topbar/Tobbar";
import Navbar from "../Navbar/Navbar";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { NavbarList } from "../Navbar/navbar.data";
import { NavItemType } from "../Navbar/Navbar.interface";
import { AppProvider } from "./context";
// import ProductsNavbar from './ProductsNavbar/ProductsNavbar';

function Header({ locale }: { locale: string }) {
  const t = useTranslations("Layout");

  const tb = t("TopBar.location");

  const menuItems = NavbarList?.map((navitem: NavItemType, index: number) => ({
    key: navitem.key,
    label: (
      <Link href={`/${locale}${navitem?.href}`} locale={locale}>
        {t(`Navbar.${navitem?.label}`)}
      </Link>
    ),
    // label:
    //   index === 1 ? (
    //     <div>
    //       <ProductsNavbar
    //         locale={locale}
    //         text={t(`Navbar.${navitem?.label}`)}
    //         AllProducts={t('AllProducts')}
    //       />
    //     </div>
    //   ) : (
    //     <Link href={`/${locale}${navitem?.href}`} locale={locale}>
    //       {t(`Navbar.${navitem?.label}`)}
    //     </Link>
    //   ),
    // href: navitem?.href,
  }));

  return (
    <AppProvider>
      <div className="sticky top-0 w-full z-50">
        <header>
          <div className="flex-col flexCenter w-full  ">
            <div className="w-full block tabletlg:hidden">
              {/* <Tobbar t={tb} number={pn?.number} /> */}
              <Tobbar t={tb} />
            </div>
            <Navbar menuItems={menuItems} locale={locale} tb={tb} />
          </div>
        </header>
      </div>
    </AppProvider>
  );
}

export default Header;
