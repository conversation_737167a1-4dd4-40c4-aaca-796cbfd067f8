import React from "react";
import { ProductCardType } from "./Product.interface";
// import Image from "next/image";

const ProductCard = ({ HideBtn = false, underline = false, imgSize, imgAlt, title, description, btnText, imgUrl, color }: ProductCardType) => {
  const words = description?.split(" ");

  const firstTenWords = words?.slice(0, 2);

  const truncatedDescription = firstTenWords?.join(" ");

  const handleHtmlContent = (htmlContent: string) => {
    return <p dangerouslySetInnerHTML={{ __html: htmlContent }} />;
  };

  return (
    <div style={{ backgroundColor: color ? color : "#fff" }} className="w-fit p-2  cursor-pointer">
      <div className="w-full flex justify-center items-center flex-col">
        <div
          className={`border-[1px] border-[#EDEDED] w-[280px] h-[280px] ${
            imgSize ? "w-[180px] h-[180px] mobilelg:w-[170px] mobilelg:h-[170px]" : "mobilelg:w-[130px] mobilelg:h-[150px]"
          } relative`}
        >
          {/* <Image src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${imgUrl}`} alt={imgAlt || "img"} layout="fill" objectFit="cover" className="object-cover" /> */}
          <img src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${imgUrl}`} alt={imgAlt || "img"} className="object-cover" />
        </div>
        <div className="text-left w-full py-4">
          <h1 className={`text-[#012A4D] text-[16px] text-center ${underline ? "underline" : ""} tabletlg:text-[14px] font-semibold py-1`}>{title}</h1>
          {/* <div className="text-gray-500 text-[12px]"> */}
          {/* {truncatedDescription && handleHtmlContent(truncatedDescription)} */}
          {/* </div> */}
        </div>
        {!HideBtn && (
          <div className="w-full">
            <button className="bg-[#006eb8] w-full font-bold text-[16px] mobile:text-[9px] text-white border-[1px] border-primary_color py-2 px-1 mobile:py-1 uppercase leading-[28px] mobile:leading-[20px] tracking-[1x]">
              {btnText}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductCard;
