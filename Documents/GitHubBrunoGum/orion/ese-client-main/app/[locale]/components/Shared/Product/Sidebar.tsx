"use client";
import React, { useEffect, useState } from "react";
import { DownOutlined, UpOutlined } from "@ant-design/icons";

interface SidebarTypes {
  catgTitle: string | undefined;
  children: React.ReactNode;
}
const Sidebar = (props: SidebarTypes) => {
  const { catgTitle, children } = props;
  const [menuOpen, setMenuOpen] = useState(true);
  const [hasRenderedChildren, setHasRenderedChildren] = useState(false);

  const toggleMenu = () => {
    setMenuOpen((prevMenuOpen) => !prevMenuOpen);
  };

  useEffect(() => {
    const handleResize = () => {
      setMenuOpen(window.innerWidth > 768);
    };
    handleResize();

    // window.addEventListener("resize", handleResize);

    return () => {
      // window.removeEventListener("resize", handleResize);
    };
  }, []);
  useEffect(() => {
    if (hasRenderedChildren) {
      setMenuOpen(false); // Close the menu when children change
    } else {
      setHasRenderedChildren(true); // Mark that children have rendered
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [children]);
  return (
    <div className="font-heebo">
      <h1
        id="menuHeader"
        className="text-[15px] pl-[1.8rem] flex tablet:hidden  tablet:items-center tablet:justify-center tablet:text-center cursor-pointer font-bold text-primary_color pb-4"
      >
        {catgTitle}
      </h1>
      <h1
        className="text-[15px] tablet:flex hidden tablet:items-center tablet:justify-center tablet:text-center cursor-pointer font-bold text-primary_color py-1"
        onClick={toggleMenu}
      >
        {menuOpen ? <UpOutlined className="pr-3" /> : <DownOutlined className="pr-3" />}
        {catgTitle}
      </h1>
      {menuOpen && children}
    </div>
  );
};

export default Sidebar;
