"use client";
import { FloatButton, Image } from "antd";
import React from "react";

const BackToTop = () => {
  // const [isVisible, setIsVisible] = useState(false);

  // useEffect(() => {
  //   const handleScroll = () => {
  //     const scrollTop = window.scrollY || document.documentElement.scrollTop;
  //     setIsVisible(scrollTop > 800);
  //   };

  //   window.addEventListener('scroll', handleScroll);

  //   return () => window.removeEventListener('scroll', handleScroll);
  // }, []);

  // const scrollToTop = () => {
  //   window.scrollTo({ top: 0, behavior: 'smooth' });
  // };

  return (
    <FloatButton.BackTop icon={<Image preview={false} src="/assets/icons/BackToTop.svg" alt="Back To Top Icon" />} />

    // <button
    //   className={`fixed h-[40px] w-[40px] p-[10px] cursor-pointer z-999 opacity-0 bg-secondry_color hover:border hover:border-solid hover:border-primary_color transition-opacity duration-400 ease-in-out bottom-[65%] left-[85%] desktop:left-[95%] mobile:!left-[85%] back-to-top-button ${
    //     isVisible ? 'visible' : ''
    //   }`}
    //   onClick={scrollToTop}
    // >
    //   <Image
    //     preview={false}
    //     src="/assets/icons/BackToTop.svg"
    //     alt="Back To Top Icon"
    //   />
    // </button>
  );
};

export default BackToTop;
