import { Col, Row } from "antd";
import React from "react";
import { useTranslations } from "next-intl";
import { NavItemChildtype, NavItemType } from "../Navbar/Navbar.interface";
import { NavbarList } from "../Navbar/navbar.data";
import Link from "next/link";
import Tobbar from "../Topbar/Tobbar";
import ProductsList from "./ProductsList";

const Footer = ({ locale }: { locale: string }) => {
  const t = useTranslations("Layout");
  const tb = t("TopBar.location");

  const products = NavbarList?.map((navitem: NavItemType) => ({
    key: navitem.key,
    label: t(`Navbar.${navitem?.label}`),
    href: navitem?.href,
    ...(navitem?.children
      ? {
          children: navitem?.children?.map((child: NavItemChildtype) => ({
            ...child,
            label: t(`Navbar.${child?.label}`),
          })),
        }
      : {}),
  }));

  return (
    <footer className="w-full py-10 bg-primary_color">
      <Row className="text-white px-12">
        <Col xxl={6} xl={6} lg={6} md={18} sm={24} xs={24} className="pr-4 py-6">
          <div>
            <Link href={`/${locale}`}>
              <img src={"/assets/images/logoFooter.png"} alt="img" className="w-[114px] h-[80px]" width={114} height={80} />
            </Link>
          </div>
          <p className="pt-3">{t("Footer.left")}</p>
        </Col>
        <Col xxl={6} xl={6} lg={6} md={12} sm={12} xs={24} className="py-6">
          <h2 className="font-bold leading-[20px] text-[16px] tracking-wide">{t("Footer.heading.one")}</h2>
          <ProductsList locale={locale} />
        </Col>
        <Col xxl={6} xl={6} lg={6} md={12} sm={12} xs={24} className="py-6 tabletlg:pl-0 pl-12">
          <h2 className="font-semibold leading-[20px] text-[16px]">{t("Footer.heading.two")}</h2>
          {products?.map((item) => {
            return (
              item?.label !== "PRODUKTE" && (
                <ul key={item?.key} className="list-none pl-0">
                  <li className="py-[.5rem] text-[14px]">
                    <Link href={`/${locale}${item?.href}`}>{item?.label}</Link>
                  </li>
                </ul>
              )
            );
          })}
        </Col>
        <Col xxl={6} xl={6} lg={6} md={18} sm={24} xs={24} className="py-6">
          <h2 className="font-bold leading-[20px] text-[16px] tracking-wide">{t("Footer.heading.three")}</h2>
          <p className="pt-2 pb-3 text-[14px]">{t("Footer.heading.three_sub_heading")}</p>
          <Tobbar flexCol={true} colReverse={true} t={tb} />
        </Col>
      </Row>
      <div className="w-full px-12 text-white pt-12 ">
        <div className="border-t-[1px] flex justify-between tabletlg:flex-col-reverse pt-7 items-left ">
          <div color="white" className="flex tabletlg:flex-col">
            <div className="tabletlg:py-3">{t("Footer.copyright.left.one")}</div>
            <div className="font-semibold px-2 tabletlg:px-0 tabletlg:py-3">{t("Footer.heading.three_sub_heading")}</div>
            <div className="tabletlg:py-3">
              <span>{t("Footer.copyright.left.two")}</span>
              <Link href={"https://www.wavenet.sk"} target="_blank" className="px-2 underline">
                {t("Footer.copyright.left.three")}
              </Link>
            </div>
          </div>
          <div className="pb-9">
            <Link href={`/${locale}/${"agbs"}`}>{t("Footer.copyright.right.one")}</Link>
            <Link href={`/${locale}/${"gdpr"}`} className="pl-2">
              {t("Footer.copyright.right.two")}
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};
export default Footer;
