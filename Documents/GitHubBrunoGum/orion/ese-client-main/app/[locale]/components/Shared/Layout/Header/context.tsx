"use client";
import getContactPhone from "@/utils/getContactPhone";
import { createContext, useState, useContext, ReactNode, useEffect } from "react";

const AppContext = createContext<AppContextType | undefined>(undefined);
interface AppContextType {
  drawerVisible: boolean;
  setDrawerVisible: (visible: boolean) => void;
  contact: any;
}
export const AppProvider = ({ children }: { children: ReactNode }) => {
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [contact, setContact] = useState("");

  useEffect(() => {
    getContactPhone().then((res) => setContact(res));
  }, []);

  return <AppContext.Provider value={{ drawerVisible, setDrawerVisible, contact }}>{children}</AppContext.Provider>;
};

export const useAppContext = () => {
  return useContext(AppContext);
};
