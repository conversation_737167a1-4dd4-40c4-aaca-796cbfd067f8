'use client';
import { usePathname, useRouter } from 'next/navigation';
import React from 'react';
interface compTypes {
  page?: number;
  categPage?: number;
  prodPage?: number;
  categoryId?: number;
  productId?: number;
  locale?: string;
}
const PaginationBtn = ({
  page,
  categPage,
  prodPage,
  categoryId,
  productId,
  locale,
}: compTypes) => {
  const router = useRouter();
  const pathname = usePathname();

  const handleLoadMore = () => {
    const nextPage = Number(page) ? Number(page) + 5 : 10;
    router.push(
      `${pathname}?${
        pathname === `/${locale}/prodbycateg` ? `categoryId=${categoryId}` : ''
      }${
        pathname === `/${locale}/product-detail` ? `productId=${productId}` : ''
      }&sidebarPage=${nextPage}&categPage=${categPage || 1}&prodPage=${
        prodPage || 1
      }`
    );
  };
  return (
    <button
      onClick={handleLoadMore}
      className="text-secondry_color tablet:text-primary_color font-bold text-[1rem]"
    >
      Load More
    </button>
  );
};

export default PaginationBtn;
