export interface ProdTypes {
  title: string;
  menuItems?: MenuItem[];
  cardBtn?: string;
  cardDesc?: string;
  cardTitle?: string;
  catgTitle: string;
}
export interface NavItemChildtype {
  key: number | string;
  label: string;
  href?: string;
  children?: NavItemChildtype[];
}

export interface NavItemType {
  key: number;
  label: string;
  href?: string | any;
  children?: NavItemChildtype[];
}
export interface MenuItem {
  key: string | number;
  label: string | any;
  children?: MenuItem[] | {} | null;
}
