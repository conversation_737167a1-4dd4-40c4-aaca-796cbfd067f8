'use client';
import React, { useEffect, useState } from 'react';
import { Menu, Spin } from 'antd';
import Link from 'next/link';
import { MenuItem } from '../Layout/Navbar/Navbar.interface';
import { ProductTypes, categoryTypes } from './Product.interface';

async function getData() {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/category/product-category`
  );
  if (!res.ok) {
    throw new Error('Failed to fetch data');
  }
  return res.json();
}

function SidebarMenu({ locale }: { locale: string }) {
  const [data, setData] = useState<categoryTypes[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [openKeys, setOpenKeys] = useState<any>([]);

  useEffect(() => {
    async function fetchData() {
      try {
        const result = await getData();
        setData(result?.categoryOrProduct || []);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  const processMenuItems = (categories: categoryTypes[]) => {
    return categories.map((category: categoryTypes) => {
      const categoryLink = (
        <>
          <Link className='text-base inline-block w-max' href={`/${locale}/prodbycateg/category/${category?.id}`}>
            {locale === 'en'
              ? category?.name
              : locale === 'de'
                ? category?.nameGe
                : locale === 'fr'
                  ? category?.nameFr
                  : ''}
          </Link>
        </>
      );

      const subcategoryLinks = Array.isArray(category?.SubCategory)
        ? category.SubCategory.map((subcategory: any) => ({
          key: `subcategory-${subcategory?.id}`,
          label: (
            <Link className='text-base'
              href={`/${locale}/prodbycateg/subCategory/${subcategory?.id}`}
            >
              {locale === 'en'
                ? subcategory?.name
                : locale === 'de'
                  ? subcategory?.nameGe
                  : locale === 'fr'
                    ? subcategory?.nameFr
                    : ''}
            </Link>
          ),
          children:
            subcategory?.Product?.length > 0
              ? subcategory.Product.map((product: ProductTypes) => ({
                key: `product-${product?.id}`,
                label: (
                  <Link className="text-base" href={`/${locale}/product-detail/${product?.id}`}>
                    {locale === 'en'
                      ? product?.title
                      : locale === 'de'
                        ? product?.titleGe
                        : locale === 'fr'
                          ? product?.titleFr
                          : ''}
                  </Link>
                ),
              }))
              : [{ key: `no-products-${subcategory.id}`, label: 'No Data' }],
        }))
        : [];

      const productLinks = Array.isArray(category?.Product)
        ? category.Product.sort((a: any, b: any) => a.productNumber - b.productNumber).map((product: ProductTypes) => ({
          key: `product-${product?.id}`,
          label: (
            <Link className='text-base' href={`/${locale}/product-detail/${product?.id}`}>
              {locale === 'en'
                ? product?.title
                : locale === 'de'
                  ? product?.titleGe
                  : locale === 'fr'
                    ? product?.titleFr
                    : ''}
            </Link>
          ),
        }))
        : [];

      return {
        key: `category-${category?.id}`,
        label: categoryLink,
        children:
          subcategoryLinks.length === 0 && productLinks.length === 0
            ? [{ key: `no-products-${category.id}`, label: 'No Data' }]
            : [...subcategoryLinks, ...productLinks],
      };
    });
  };

  const processedMenuItems: MenuItem[] = data ? processMenuItems(data) : [];

  const handleOpenChange = (keys: any) => {
    const latestOpenKey = keys.find((key: any) => !openKeys.includes(key));

    if (latestOpenKey) {
      // Get the parent key of the latest opened submenu
      const parentKey = latestOpenKey.split('-').slice(0, -1).join('-');

      // Filter out submenus that are siblings (have the same parent)
      const newOpenKeys = openKeys.filter((key: any) => {
        const keyParentKey = key.split('-').slice(0, -1).join('-');
        return keyParentKey !== parentKey;
      });

      // Add the latest opened submenu to the open keys
      setOpenKeys([...newOpenKeys, latestOpenKey]);
    } else {
      // Close all submenus if none is newly opened
      setOpenKeys(keys);
    }
  };

  return (
    <div>
      {loading ? (
        <Spin className="pl-[25%]" />
      ) : (
        <Menu
          id="product-menu"
          mode="inline"
          openKeys={openKeys}
          onOpenChange={handleOpenChange}
          className="menu-sidebar flex-wrap flex-col bg-transparent tablet:flex tablet:text-center tablet:justify-center tablet:items-center border-0 text-base font-heebo"
          items={processedMenuItems}
          disabledOverflow
        />
      )}
    </div>
  );
}

export default SidebarMenu;
