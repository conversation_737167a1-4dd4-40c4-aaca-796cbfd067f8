import React from "react";
import SubTitle from "../SubTitle/SubTitle";
// import Image from "next/image";

const Product = ({ img, text }: { img: string; text: string }) => {
  return (
    <div className="flexCenter w-[330px] min-h-[300px] h-[300px] flex-col m-2">
      <img src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${img}`} alt="Product Image" width={"300"} height={"300"} className="w-[300px] h-full object-cover" />
      <div className="inset-0 flexCenter">
        <span className="bg-secondry_color max-w-[300px] px-2 py-2 flexCenter text-center border border-solid border-primary_color cursor-pointer">
          <SubTitle color="white" text={text} />
        </span>
      </div>
    </div>
  );
};

export default Product;

// blur-[2px]
// opacity-100 bg-white bg-opacity-50
