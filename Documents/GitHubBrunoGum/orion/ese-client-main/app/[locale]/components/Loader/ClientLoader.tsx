// components/ClientLoader.tsx
"use client"
import React, { useState, useEffect } from 'react';
import Loader from './Loader';

const ClientLoader = ({ children }: any) => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const handleCssLoad = () => {
      const testElement = document.createElement("div");
      testElement.className = "loader";
      document.body.appendChild(testElement);
      const isLoaded = window.getComputedStyle(testElement).position === 'fixed';
      document.body.removeChild(testElement);

      if (isLoaded) {
        setLoading(false);
      }
    };

    handleCssLoad();
    const intervalId = setInterval(handleCssLoad, 100);
    return () => clearInterval(intervalId);
  }, []);

  if (loading) {
    return <Loader />;
  }

  return <>{children}</>;
};

export default ClientLoader;
