import React from 'react';
import Title from '../Shared/Title/Title';
import Product from '../Shared/Product/Product';
import { Col, Row } from 'antd';
import Link from 'next/link';
import { categoryTypes } from '../Shared/Product/Product.interface';
import PaginationComp from './PaginationControls';

interface ProdOverviewTypes {
  title?: boolean;
  mainTitle?: string;
  locale: string;
  page: number;
  categoryId?: number;
  productPage?: number;
  sidebarPage?: number;
}
async function getData({ skip, take }: { skip: number; take: number }) {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/category/big-category?skip=${skip}&take=${take}`,
    {
      // next: { revalidate: 1 },
      cache: 'no-cache',
    }
  );

  if (!res.ok) {
    throw new Error('Failed to fetch data');
  }

  return res.json();
}
const ProductsOverview = async ({
  title = true,
  mainTitle,
  locale,
  page,
  categoryId,
  productPage,
  sidebarPage,
}: ProdOverviewTypes) => {
  const take = 9999999999999;
  const skip = (Number(page) - 1) * take;
  const data = await getData({ skip, take });
  return (
    <div className='pb-12'>
      {title && (
        <Row className="flexCenter py-[60px] mobile:!py-[10px] text-center">
          <Title text={mainTitle} />
        </Row>
      )}
      <Row gutter={[0, 50]} className="flexCenter py-10 mobile:!py-0">
        {data?.categories?.map((category: categoryTypes) => (
          <Col
            xxl={8}
            xl={8}
            lg={12}
            md={12}
            sm={24}
            xs={24}
            key={category?.id}
            className="flexCenter p-2 tablet:px-5 mobile:!px-0"
          >
            <Link
              href={`/${locale}/prodbycateg?bigCategoryId=${category?.id}`}
              className="h-auto flexCenter w-full"
            >
              <Product
                img={category?.File?.url}
                text={`${
                  locale === 'en'
                    ? category?.name
                    : locale === 'de'
                    ? category?.nameGe
                    : locale === 'fr'
                    ? category?.nameFr
                    : ''
                }`}
              />
            </Link>
          </Col>
        ))}
      </Row>
      {/* <div className="w-full flexCenter"> */}
      {/*   <PaginationComp */}
      {/*     total={data && data?.count} */}
      {/*     productPage={productPage} */}
      {/*     categoryId={categoryId} */}
      {/*     locale={locale} */}
      {/*     sidebarPage={sidebarPage} */}
      {/*     skip={skip} */}
      {/*     take={take} */}
      {/*   /> */}
      {/* </div> */}
    </div>
  );
};

ProductsOverview.defaultProps = {
  title: true,
};

export default ProductsOverview;
