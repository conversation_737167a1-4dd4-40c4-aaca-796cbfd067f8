'use client';
import React from 'react';
import { Pagination } from 'antd';
import { usePathname, useRouter } from 'next/navigation';
interface compTypes {
  total: number;
  categoryId?: number;
  locale: string;
  productPage?: number;
  sidebarPage?: number;
  skip: number;
  take: number;
}
const PaginationComp = ({
  total,
  categoryId,
  locale,
  productPage,
  sidebarPage,
  skip,
  take,
}: compTypes) => {
  const router = useRouter();
  const pathname = usePathname();
  const handlePagenation = (page: number) => {
    router.push(
      `${pathname}?${
        pathname === `/${locale}/prodbycateg` ? `categoryId=${categoryId}` : ''
      }&categPage=${page}&page=${productPage || 1}&sidebarPage=${
        sidebarPage || 10
      }`
    );
  };
  return (
    <div className="flexCenter my-[50px]">
      <Pagination
        defaultCurrent={skip / take + 1 || 1}
        total={total && total}
        pageSize={take}
        onChange={handlePagenation}
      />
    </div>
  );
};
export default PaginationComp;
