export interface ProductType {
  id: number;
  img: string;
  text: string;
}

export const ProductList: ProductType[] = [
  {
    id: 1,
    img: '../assets/images/product-1.svg',
    text: 'product_one_text',
  },
  {
    id: 2,
    img: '../assets/images/product-2.svg',
    text: 'product_two_text',
  },
  {
    id: 3,
    img: '../assets/images/product-3.svg',
    text: 'product_three_text',
  },
  {
    id: 4,
    img: '../assets/images/product-4.svg',
    text: 'product_four_text',
  },
  {
    id: 5,
    img: '../assets/images/product-5.svg',
    text: 'product_five_text',
  },
  {
    id: 6,
    img: '../assets/images/product-6.svg',
    text: 'product_six_text',
  },
  {
    id: 7,
    img: '../assets/images/product-7.svg',
    text: 'product_seven_text',
  },
  {
    id: 8,
    img: '../assets/images/product-8.svg',
    text: 'product_eight_text',
  },
  {
    id: 9,
    img: '../assets/images/product-9.svg',
    text: 'product_nine_text',
  },
];
