import React from 'react';
import { Col, Pagination, Row } from 'antd';
import Sidebar from '../Shared/Product/Sidebar';
import ProductsOverview from '../Products/Products';
import SidebarMenu from '../Shared/Product/SidebarMenu';
import { useTranslations } from 'next-intl';
// import ProductByCat from '../Shared/Product/ProductByCat';
import Title from '../Shared/Title/Title';
interface compTypes {
  catgTitle: string;
  locale: string;
  page: number;
  sidebarPage?: number;
  pagination: number;
}
const ProductComp = ({
  catgTitle,
  locale,
  page,
  sidebarPage,
  pagination,
}: compTypes) => {
  const bigCategories = useTranslations('Big-Categories');

  return (
    <div className="w-full">
      <Row className="flex justify-center">
      
        <Col
          xxl={24}
          xl={24}
          lg={24}
          md={24}
          sm={24}
          xs={24}
          className="mobile:px-4 px-6"
        >
          {/* <h1 className="text-[60px] mobile:text-[30px] text-center font-bold text-gray-800 py-9">
            {title}
          </h1> */}
          <div className="hidden tablet:flex justify-center pb-4 border-2 bg-[#1A75CF] txtWhite pt-3">
            <Sidebar catgTitle={catgTitle}>
              <SidebarMenu
                locale={locale}
              />
            </Sidebar>
          </div>
          {/* <div>
            <ProductByCat
              title={title}
              page={pagination}
              locale={locale}
              sidebarPage={sidebarPage}
              catgTitle={catgTitle}
            />
          </div> */}
          <Title text={bigCategories('title')} />
          <ProductsOverview title={false} locale={locale} page={page} />
        </Col>
      </Row>
    </div>
  );
};

export default ProductComp;
