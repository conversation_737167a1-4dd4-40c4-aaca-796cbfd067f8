import React from "react";
import Paragraph from "../Shared/Paragraph/Paragraph";

async function getData() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/impressum`, {
    next: { revalidate: 1 },
    cache: "no-store",
  });

  if (!res.ok) {
    throw new Error("Failed to fetch data");
  }

  return res.json();
}

const ImprintText = async ({ locale }: { locale: string }) => {
  const data = await getData();
  return (
    <div>
      <div className="max-w-[730px] mx-auto text-center px-4 pb-20 mobile:pb-14">
        <Paragraph
          text={locale === "en" ? data?.impressum[0]?.description : locale === "fr" ? data?.impressum[0]?.descriptionFr : data?.impressum[0]?.descriptionGe}
        />
      </div>
    </div>
  );
};

export default ImprintText;
