'use client';
import React, { useCallback } from 'react';
import { GoogleMap, useJs<PERSON><PERSON><PERSON>oa<PERSON>, <PERSON><PERSON> } from '@react-google-maps/api';

interface Center {
  lat: number;
  lng: number;
}

const customLocation: Center = {
  lat: 51.0067568,
  lng: 6.8699363,
};

function Map(): JSX.Element {
  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: 'AIzaSyBRYrZl5hvt_4TAT7mY_58PwVopt2cHtlE',
  });

  const onLoad = useCallback(function callback(mapInstance: google.maps.Map) {
    mapInstance.setCenter(customLocation);
    mapInstance.setZoom(10);
  }, []);

  return isLoaded ? (
    <GoogleMap
      center={customLocation}
      mapContainerClassName="max-w-[900px] h-[640px] tabletlg:h-[450px]"
      zoom={20}
      onLoad={onLoad}
    >
      {isLoaded && <Marker position={customLocation} />}
    </GoogleMap>
  ) : (
    <></>
  );
}

export default React.memo(Map);
