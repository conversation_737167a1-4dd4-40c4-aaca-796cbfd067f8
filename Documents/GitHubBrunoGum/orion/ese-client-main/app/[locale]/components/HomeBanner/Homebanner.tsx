import React from 'react';
import { useTranslations } from 'next-intl';
import SubTitle from '../Shared/SubTitle/SubTitle';
import HomeImage from './HomeImage';

const HomeBanner = () => {
  const t = useTranslations('Home');
  return (
    <>
      <HomeImage />
      <div className="bg-primary_color max-h-[140px] !font-normal mt-[-8px] h-[140px] min-h-[70px] tablet:h-[100px] mobile:h-[70px] flexCenter text-center">
        <SubTitle
          color="#ffffff"
          text={t('HomeBanner.bannerFooter')}
          textNormal={true}
        />
      </div>
    </>
  );
};

export default HomeBanner;
