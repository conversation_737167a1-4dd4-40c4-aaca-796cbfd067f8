"use client";
import { Spin } from "antd";
import Image from "next/image";
import React, { useState } from "react";

const HomeImage = () => {
  const [loading, setLoading] = useState(true);

  return (
    <div className="relative w-[100%] pb-[35%] tablet:pb-[35%] mobile:pb-[25%]">
      {loading && (
        <div className="flexCenter pt-[15%] mobile:pt-[50%] ">
          <Spin />
        </div>
      )}

      <Image
        src="/assets/images/Dizajn-bez-názvu-4.png"
        alt="Home Banner"
        className="object-cover object-center"
        layout="fill"
        objectFit="cover"
        objectPosition="center"
        onLoadingComplete={() => setLoading(false)}
        unoptimized={true} // Disables optimization
      />
    </div>
  );
};

export default HomeImage;
