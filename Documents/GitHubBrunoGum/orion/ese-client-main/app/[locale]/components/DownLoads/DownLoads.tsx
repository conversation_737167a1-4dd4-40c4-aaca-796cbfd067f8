import React from 'react';
import DownLoadCard from './DownLoadsCard';
import PaginationControls from './PaginationControls';

async function getData({ skip, take }: { skip: number; take: number }) {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/download?skip=${skip}&take=${take}`,
    {
      // next: { revalidate: 1 },
      cache: 'no-cache',
    }
  );
  if (!res.ok) {
    throw new Error('Failed to fetch data');
  }
  return res.json();
}

const DownLoads = async ({
  page,
  locale,
}: {
  page: number;
  locale: string;
}) => {
  const take = 10;
  const skip = (Number(page) - 1) * take;
  const currentItems = await getData({ skip, take });
  return (
    <>
      <div className="flex justify-center items-center w-full flex-col px-[40px] tablet:px-[10px]">
        {currentItems?.downloads?.map((item: any, index: number) => {
          return <DownLoadCard locale={locale} item={item} key={index} />;
        })}
      </div>
      <PaginationControls
        currentPage={skip}
        total={currentItems?.count}
        locale={locale}
        pageSize={take}
      />
    </>
  );
};

export default DownLoads;
