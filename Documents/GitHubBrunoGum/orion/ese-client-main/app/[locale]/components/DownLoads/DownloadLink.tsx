"use client";
import React from "react";

const DownloadLink = ({ link, title }: { link: string; title: string }) => {
  const handleDownload = (url: string) => {
    const link = document.createElement("a");
    link.href = url;
    link.download = "";
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  return (
    <div
      className="flex justify-start items-start w-full underline cursor-pointer text-[16px] mobilelg:text-[14px] font-bold max-w-[1200px] text-ellipsis"
      onClick={() => handleDownload(`${process.env.NEXT_PUBLIC_BACKEND_URL}/${link} `)}
    >
      <span className="lines">{title}</span>
    </div>
  );
};

export default DownloadLink;
