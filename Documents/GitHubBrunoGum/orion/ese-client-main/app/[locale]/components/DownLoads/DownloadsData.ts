export interface DownloadItem {
  downloads_title: string;
  link: { download_link: string }[];
}

export interface DownloadCardType {
  item: any;
  downloads: any;
  title: string;
  titleGe: string;
  titleFr: string;
  Files: any;
}

export const downLoads = [
  {
    downloads_title: 'downloads_title_one',
    link: [
      {
        download_link: 'download_link_one',
      },
    ],
  },
  {
    downloads_title: 'downloads_title_two',
    link: [
      {
        download_link: 'download_link_two',
      },
      {
        download_link: 'download_link_three',
      },
      {
        download_link: 'download_link_four',
      },
      {
        download_link: 'download_link_five',
      },
    ],
  },
];
