import React from "react";
import { DownloadCardType } from "./DownloadsData";
import DownloadLink from "./DownloadLink";

interface Props {
  item: DownloadCardType;
  locale: string;
}

const DownLoadCard: React.FC<Props> = ({ item, locale }) => {
  return (
    <div className="flexCenter flex-col w-full border-2 border-solid p-[20px] mobile:mt-5">
      <div className="flex justify-start items-center w-full text-[#006EB8] font-[700] text-[28px] mobilelg:text-[20px]  leading-9  mb-[30px]">
        {locale === "en" ? item?.title : locale === "fr" ? item?.titleFr : item?.titleGe}
      </div>
      <div className="flex justify-start items-start w-full flex-col gap-y-[5px]">
        {item?.Files?.map((link: any) => {
          const title = link.url.replace("uploads/", "").split("-").slice(1);
          return <DownloadLink title={title} key={link?.id} link={link?.url} />;
        })}
      </div>
    </div>
  );
};

export default DownLoadCard;
