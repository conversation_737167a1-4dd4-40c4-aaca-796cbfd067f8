"use client";
import React, { useState } from "react";
import type { FormProps } from "antd";
import { Button, Checkbox, Form, Input, message } from "antd";
import SubTitle from "../Shared/SubTitle/SubTitle";
import { CheckboxChangeEvent } from "antd/es/checkbox";

type FieldType = {
  name?: string;
  email?: string;
  phone?: string;
  message?: string;
  checkbox?: string;
};

async function postData({ data }: { data: any }) {
  const res = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/contact`, {
    method: "POST",
    body: JSON.stringify(data),
    headers: {
      "Content-Type": "application/json",
    },
  });

  if (res?.status === 404) {
    message?.error(res?.statusText);
  } else {
    message.success("Email was sended");
  }
}

const ContactForm = ({
  name,
  nameAlert,
  email,
  emailAlert,
  phone,
  message,
  submit,
  checkBoxAlert,
  phoneAlert,
  checkboxText,
  checkboxLink,
  checkboxSuffix,
  checkboxUrl,
  locale,
}: {
  name: string;
  nameAlert: string;
  email: string;
  emailAlert: string;
  phone: string;
  message: string;
  submit: string;
  checkBoxAlert: string;
  phoneAlert: string;
  checkboxText: string;
  checkboxLink: string;
  checkboxSuffix: string;
  checkboxUrl: string;
  locale: string;
}) => {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);

  const onFinish: FormProps<FieldType>["onFinish"] = (values) => {
    setIsLoading(true);
    const info = {
      name: values?.name,
      email: values?.email,
      phone: values?.phone,
      news: values?.message,
    };
    postData({ data: info });
    form.resetFields();
    setIsLoading(false);
  };

  return (
    <Form form={form} name="basic" style={{ maxWidth: 900 }} initialValues={{ remember: false }} onFinish={onFinish} disabled={isLoading} autoComplete="off">
      <Form.Item<FieldType> name="name" rules={[{ required: true, message: `${nameAlert}` }]}>
        <Input className="h-[54px]" placeholder={name} />
      </Form.Item>

      <Form.Item<FieldType> name="email" rules={[{ required: true, message: `${emailAlert}` }]}>
        <Input className="h-[54px]" placeholder={email} />
      </Form.Item>

      <Form.Item<FieldType> name="phone" rules={[{}]}>
        <Input className="h-[54px]" placeholder={phone} />
      </Form.Item>

      <Form.Item<FieldType> name="message">
        <Input.TextArea placeholder={message} rows={10} />
      </Form.Item>

      <Form.Item<FieldType> valuePropName="checked" name="checkbox" wrapperCol={{ span: 20 }} rules={[{ required: true, message: `${checkBoxAlert}` }]}>
        <Checkbox className="font-normal text-[13px] text-secondry_color">
          {checkboxText}
          <a href={`/${locale}/${checkboxUrl}`} style={{ textDecoration: "underline" }}>
            {checkboxLink}
          </a>
          {checkboxSuffix}
        </Checkbox>
      </Form.Item>

      <Form.Item>
        <Button
          className="flexCenter p-6 !bg-secondry_color !border-secondry_color w-[100%] hover:!bg-secondry_color hover:!border-secondry_color"
          htmlType="submit"
          loading={isLoading}
          disabled={isLoading}
        >
          <SubTitle color="white" text={submit} />
        </Button>
      </Form.Item>
    </Form>
  );
};

export default ContactForm;
