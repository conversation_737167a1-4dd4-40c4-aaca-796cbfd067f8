import { useTranslations } from 'next-intl';
import React from 'react';

function ProductDescription({ prodDetails }: { prodDetails: any }) {
  const t = useTranslations('Product_Detail')


  const handleHtmlContent = (htmlContent: string) => {
    return <div dangerouslySetInnerHTML={{ __html: htmlContent }} />;
  };

  return (
    <div className="flexCenter w-full  flex-col pt-12 ">
      <div className='flex justify-center items-center my-[90px] mobilelg:my-[40px] w-full'>
        <h1 className=" my-[10px] text-primary_color text-[43px] font-bold tablet:text-[33px] mobile:!text-2xl">
          {t('detail')}
        </h1>
      </div>
      {prodDetails && (
        <div className="flexCenter w-full border-2 border-[#EDEDED] ">
          {handleHtmlContent(prodDetails)}
        </div>
      )}
    </div>
  );
}

export default ProductDescription;
