'use client'
import React from 'react';
const ProdDetailsBtn = ({ text, download, link }: { text: string, link?: any, download: Boolean }) => {

  const handleDownload = (url: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = '';
    link.target = '_blank'; // Add this line to force download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link)
  };


  return (
    <button onClick={() => download ? handleDownload(link) : ""} className="bg-[#006eb8] w-full  font-bold text-[16px] mobile:text-[14px] text-white border-[1px] border-primary_color py-2 px-1 mobile:py-1 uppercase leading-[28px] mobile:leading-[20px] tracking-[1x]">
      {text}
    </button>
  );
};

export default ProdDetailsBtn;
