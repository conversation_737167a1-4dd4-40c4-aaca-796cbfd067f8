"use client";
import React, { useEffect, useState } from "react";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import ProductGalleryModal from "./ProductGalleryModal/ProductGalleryModal";
import { fileTypes } from "./ProdTypes";
// import Image from 'next/image';

function ProductGallery({ files, imgAlt }: { files: fileTypes[]; imgAlt: string }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeImage, setActiveImage] = useState<string>(`${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[0].url}`);

  const settings = {
    dots: false,
    infinite: true,
    speed: 100,
    autoplay: true,
    beforeChange: (_: any, n: any) => {
      setActiveImage(`${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[n].url}`);
    },
    autoplaySpeed: 10000,
    slidesToShow: 3,
    vertical: false,
    slidesToScroll: 1,
    arrows: true,
    prevArrow: (
      <div>
        <LeftOutlined style={{ fontSize: "20px", color: "black", paddingBottom: "30px" }} />
      </div>
    ),
    nextArrow: (
      <div>
        <RightOutlined style={{ fontSize: "20px", color: "black", paddingBottom: "30px" }} />
      </div>
    ),
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  return (
    <div className="flex justify-center items-center flex-col w-full gap-[10px] mt-[70px] tablet:mt-[30px]">
      <div onClick={showModal} className="flex justify-center items-center cursor-pointer  border-2 border-solid  border-[#EDEDED]">
        {/* <Image */}
        {/*   src={activeImage || `${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[0]?.url}`} */}
        {/*   alt={imgAlt || "product Image"} */}
        {/*   width={362} */}
        {/*   height={360} */}
        {/*   objectFit="cover" */}
        {/* /> */}
        <img
          src={activeImage || `${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[0]?.url}`}
          alt={imgAlt || "product Image"}
          width={362}
          height={360}
          className="object-cover"
        />
      </div>
      <div className="flex  justify-center items-center  w-full   ">
        <Slider className="  h-[100px] w-full max-w-[300px] " {...settings}>
          {files?.map((file) => {
            return (
              <div
                key={file.id}
                onClick={() => setActiveImage(`${process.env.NEXT_PUBLIC_BACKEND_URL}/${file?.url}`)}
                className="flex justify-center items-center w-full max-w-[89px] h-full max-h-[89px] cursor-pointer border-2 border-solid border-[#EDEDED]"
              >
                {/* <Image src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${file?.url}`} alt="product Image" width={87} height={87} objectFit="cover" /> */}
                <img src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${file?.url}`} alt="product Image" width={87} height={87} className="object-cover" />
              </div>
            );
          })}
        </Slider>
      </div>
      <ProductGalleryModal
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
        files={files}
        idx={activeImage ? files.findIndex((i) => activeImage.endsWith(i.url)) : -1}
      />
    </div>
  );
}

export default ProductGallery;
