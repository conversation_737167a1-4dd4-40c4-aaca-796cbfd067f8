"use client";
import React, { useState } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import ProductGalleryModal from "../ProductGallery/ProductGalleryModal/ProductGalleryModal";
import { fileTypes } from "../ProductGallery/ProdTypes";
// import Image from 'next/image';

function ProductSliderMobile({ files, imgAlt }: { files: fileTypes[]; imgAlt: string }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);

  const settings = {
    dots: false,
    infinite: false,
    speed: 100,
    autoplay: true,
    autoplaySpeed: 10000,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: true,
    afterChange: (current: any) => {
      setCurrentSlide(current);
    },
    prevArrow: (
      <div>
        <LeftOutlined style={{ fontSize: "20px", color: "black", paddingBottom: "30px" }} />
      </div>
    ),
    nextArrow: (
      <div>
        <RightOutlined style={{ fontSize: "20px", color: "black", paddingBottom: "30px" }} />
      </div>
    ),
    responsive: [
      {
        breakpoint: 850,
        settings: {
          // vertical: true,
        },
      },
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 3,
        },
      },
    ],
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  return (
    <div className="flex justify-center items-center flex-col w-full h-full  border-2 border-solid border-[#EDEDED]">
      <Slider className="flex justify-center items-center  h-full   max-h-[352px] w-full max-w-[352px]  " {...settings}>
        {files?.map((file, index) => {
          return (
            <div key={file?.id} onClick={showModal} className=" flex justify-center items-center  w-full  h-full max-h-[352px] cursor-pointer relative">
              {/* <Image */}
              {/*   src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${file?.url}`} */}
              {/*   alt={imgAlt || "product Image"} */}
              {/*   width={352} */}
              {/*   height={352} */}
              {/*   objectFit="cover" */}
              {/* /> */}
              <img
                src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${file?.url}`}
                alt={imgAlt || "product Image"}
                width={352}
                height={352}
                className="object-cover"
              />
              <div onClick={() => setCurrentSlide(index)} className="flex justify-end items-center w-full p-[20px] absolute bottom-0 right-0">
                <div className=" text-white font-[600] bg-black px-[15px] py-[5px]  ">{`${currentSlide + 1}/${files.length}`}</div>
              </div>
            </div>
          );
        })}
      </Slider>

      <ProductGalleryModal files={files} idx={currentSlide} isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} />
    </div>
  );
}

export default ProductSliderMobile;
