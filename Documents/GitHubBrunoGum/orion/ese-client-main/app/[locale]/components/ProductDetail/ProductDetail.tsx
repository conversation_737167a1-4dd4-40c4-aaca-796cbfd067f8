import React from 'react';
import Paragraph from '../Shared/Paragraph/Paragraph';
import { Breadcrumb, Col, Row } from 'antd';
import ProductGallery from './ProductGallery/ProductGallery';
import ProductDescription from './ProductDescription/ProductDescription';
import Sidebar from '../Shared/Product/Sidebar';
import Link from 'next/link';
import ProductSliderMobile from './ProductSliderMobile/ProductSliderMobile';
import SidebarMenu from '../Shared/Product/SidebarMenu';
import ProdDetailsBtn from './ProdDetailsBtn/ProdDetailsBtn';
import Title from '../Shared/Title/Title';

async function getData(productId: number) {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/product/${productId}`,
    {
      // next: { revalidate: 1 },
      cache: 'no-cache',
    }
  );

  if (!res.ok) {
    throw new Error('Failed to fetch data');
  }

  return res.json();
}

async function ProductDetail({
  productId,
  catgTitle,
  locale,
  prodTitle,
  sidebarPage,
  downloadText,
  contactText,
}: {
  contactText: string;
  downloadText: string;
  productId: number;
  catgTitle: string;
  prodTitle?: string;
  sidebarPage?: number;
  locale: string;
}) {
  const data = productId !== null && (await getData(productId));
  const product = data?.existProduct;
  return (
    <div className="flex justify-center items-center mobile:px-3 w-full">
      <Row gutter={[16, 16]} className="flex justify-start items-start  w-full">

        <Col
          xs={24}
          sm={24}
          md={24}
          lg={24}
          xl={24}
          xxl={24}
          style={{ borderLeft: '2px solid #EDEDED ' }}
          className="flex justify-center items-center flex-col  w-full py-[30px]  tablet:!border-none"
        >
          <Row
            gutter={[16, 16]}
            className="flex justify-start items-center w-full"
          >
            <Col xs={24} sm={24} md={24} lg={24} xl={10} xxl={10}>
              <div className="pl-3 pb-4 tablet:pl-1 ">
                <Breadcrumb
                  items={[
                    {
                      href: `/${locale}`,
                      title: (
                        <span className="text-black text-[12px]">Home</span>
                      ),
                    },
                    {
                      href: `/${locale}/products`,

                      title: (
                        <span className="text-black text-[12px]">
                          {prodTitle}
                        </span>
                      ),
                    },
                    product?.Category?.id && {
                      href: `/${locale}/prodbycateg/category/${product?.Category?.id}`,
                      title: (
                        <span className="text-black text-[12px]">
                          {`${locale === 'en'
                              ? product?.Category?.name
                              : locale === 'de'
                                ? product?.Category?.nameGe
                                : locale === 'fr'
                                  ? product?.Category?.nameFr
                                  : ''
                            }`}
                        </span>
                      ),
                    },
                    {
                      title: (
                        <span className="text-black text-[12px]">
                          {`${locale === 'en'
                              ? product?.title
                              : locale === 'de'
                                ? product?.titleGe
                                : locale === 'fr'
                                  ? product?.titleFr
                                  : ''
                            }`}
                        </span>
                      ),
                    },
                  ]}
                />
              </div>
              <div className="hidden tablet:flex justify-center pb-4 border-2 bg-[#1A75CF] txtWhite  pt-3">
                <Sidebar catgTitle={catgTitle}>
                  <SidebarMenu
                    locale={locale}
                  />
                </Sidebar>
              </div>
              <div className="hidden tablet:flex tablet:justify-center">
                <div className="text-primary_color flexCenter py-[60px] mobile:!py-[30px] text-center text-4xl  font-bold tablet:text-4xl mobile:!text-2xl">
                  {locale === 'en'
                    ? product?.title
                    : locale === 'de'
                      ? product?.titleGe
                      : locale === 'fr'
                        ? product?.titleFr
                        : ''}
                </div>
              </div>
              <div className="mobilelg:hidden">
                <ProductGallery files={product?.Files.sort((a:any, b:any) => a.id - b.id)} imgAlt={product.imgAlt} />
              </div>
              <div className="  mobilelg:block   hidden">
                <ProductSliderMobile files={product?.Files} imgAlt={product.imgAlt} />
              </div>
            </Col>
            <Col xs={24} sm={24} md={24} lg={24} xl={14} xxl={14}>
              <div className="flex justify-start tablet:hidden">
                <div className="text-primary_color flexCenter py-[60px] mobile:!py-[30px] text-center text-4xl  font-bold tablet:text-4xl mobile:!text-2xl">
                  {locale === 'en'
                    ? product?.title
                    : locale === 'de'
                      ? product?.titleGe
                      : locale === 'fr'
                        ? product?.titleFr
                        : ''}
                </div>
              </div>
              <Paragraph
                text={`${locale === 'en'
                    ? product?.description
                    : locale === 'de'
                      ? product?.descriptionGe
                      : locale === 'fr'
                        ? product?.descriptionFr
                        : ''
                  }`}
              />
              <div className="flex justify-start items-center w-full gap-x-[20px] mobilelg:flex-col gap-y-[15px] mt-[30px] ">
                <div className="w-full max-w-[376px]  mobilelg:max-w-full">
                  <ProdDetailsBtn
                    text={downloadText}
                    link={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${data?.existProduct?.Pdf?.url}`}
                    download={true}
                  />
                </div>
                <div className="w-full max-w-[116px] mobilelg:max-w-full ">
                  <Link href={`/${locale}/contact-us`}>
                    <ProdDetailsBtn text={contactText} download={false} />
                  </Link>
                </div>
              </div>
            </Col>
          </Row>
          <ProductDescription
            prodDetails={`${locale === 'en'
                ? product?.moreDetail
                : locale === 'de'
                  ? product?.moreDetailGe
                  : locale === 'fr'
                    ? product?.moreDetailFr
                    : ''
              }`}
          />
        </Col>
      </Row>
    </div>
  );
}

export default ProductDetail;
