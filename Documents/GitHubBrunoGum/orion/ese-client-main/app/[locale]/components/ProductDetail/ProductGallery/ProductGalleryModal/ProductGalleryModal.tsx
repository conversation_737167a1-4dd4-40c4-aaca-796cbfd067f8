"use client";
import { Modal } from "antd";
import React, { useEffect, useRef, useState } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import useWindowSize from "@/hooks/windowDimensions";
import { fileTypes } from "../ProdTypes";
// import Image from 'next/image';

function ProductGalleryModal({
  isModalOpen,
  setIsModalOpen,
  idx,
  files,
}: {
  isModalOpen: boolean;
  setIsModalOpen: (value: boolean) => void;
  files: fileTypes[];
  idx: number;
}) {
  const [currentSlide, setCurrentSlide] = useState(idx);
  const { width } = useWindowSize();
  const sliderRef = useRef(null); // Reference to the Slider component

  useEffect(() => {
    setCurrentSlide(idx);
    // @ts-ignore
    sliderRef?.current?.slickGoTo?.(idx); // Set the current slide programmatically
  }, [idx]);

  const goToSlide = (index: number) => {
    // @ts-ignore
    sliderRef?.current?.slickGoTo?.(index); // Set the current slide programmatically
  };

  const settings = {
    dots: false,
    infinite: true,
    speed: 100,
    autoplay: true,
    autoplaySpeed: 10000,
    slidesToShow: 3,
    slidesToScroll: 1,
    centerMode: true,
    centerPadding: "20px",
    arrows: true,
    afterChange: (index: number) => setCurrentSlide(index), // Get the current active slide
    prevArrow: (
      <div>
        <LeftOutlined style={{ fontSize: "20px", color: "black", paddingBottom: "30px" }} />
      </div>
    ),
    nextArrow: (
      <div>
        <RightOutlined style={{ fontSize: "20px", color: "black", paddingBottom: "30px" }} />
      </div>
    ),
    responsive: [
      {
        breakpoint: 850,
        settings: {
          // vertical: true,
        },
      },
      {
        breakpoint: 500,
        settings: {
          slidesToShow: 6,
          slidesToScroll: 6,
          // vertical: true,
          arrows: false,
        },
      },
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 3,
        },
      },
    ],
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <Modal centered footer={false} className="product_Gallery_Modal" open={isModalOpen} onCancel={handleCancel}>
      <div className="flex justify-center items-center flex-col w-full gap-[10px] ">
        <div className="absolute left-4 top-[40%] cursor-pointer" onClick={() => goToSlide(currentSlide - 1)}>
          <LeftOutlined style={{ fontSize: "50px", color: "#454545" }} />
        </div>

        <div className="absolute right-4 top-[40%] cursor-pointer" onClick={() => goToSlide(currentSlide + 1)}>
          <RightOutlined
            style={{
              fontSize: "50px",
              color: "#454545",
            }}
          />
        </div>

        {width >= 500 ? (
          <div className="flex justify-center items-center w-full  ">
            {/* <Image */}
            {/*   src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[currentSlide]?.url}` || `${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[0]?.url}`} */}
            {/*   alt="product Image" */}
            {/*   width={500} */}
            {/*   height={500} */}
            {/*   objectFit='cover' */}
            {/* /> */}
            <img
              src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[currentSlide]?.url}` || `${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[0]?.url}`}
              alt="product Image"
              width={500}
              height={500}
              className="object-cover"
            />
          </div>
        ) : (
          <div className="flex justify-center items-center w-full  ">
            {/* <Image */}
            {/*   src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[currentSlide]?.url}` || `${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[0]?.url}`} */}
            {/*   alt="product Image" */}
            {/*   width={352} */}
            {/*   height={352} */}
            {/*   objectFit="cover" */}
            {/* /> */}
            <img
              src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[currentSlide]?.url}` || `${process.env.NEXT_PUBLIC_BACKEND_URL}/${files[0]?.url}`}
              alt="product Image"
              width={352}
              height={352}
              className="object-cover"
            />
          </div>
        )}
        {width <= 500 && (
          <div className="flex justify-center items-center w-full p-[20px]  ">
            <div className=" text-[#012A4D] font-[600] px-[15px] py-[5px]  ">1/4 </div>
          </div>
        )}
        <div className="flex  justify-center items-center  w-full">
          <Slider
            className={width >= 500 ? "  h-[100px] w-full max-w-[400px] gap-x-[20px]" : "  w-full max-w-[400px]  gap-x-[20px] py-[10px] bg-[#EDEDED] "}
            {...settings}
            ref={sliderRef}
          >
            {files?.map((file: fileTypes, idx) => {
              return width >= 500 ? (
                <div
                  key={file?.id}
                  onClick={() => {
                    goToSlide(idx);
                  }}
                  className="flex justify-center items-center  w-full max-w-[100px] h-full max-h-[100px] border-2 cursor-pointer border-solid border-[#EDEDED]"
                >
                  {/* <Image src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${file?.url}`} alt="product Image" width={100} height={100} objectFit="cover" /> */}
                  <img src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${file?.url}`} alt="product Image" width={100} height={100} className="object-cover" />
                </div>
              ) : (
                <div
                  key={file.id}
                  onClick={() => {
                    goToSlide(idx);
                  }}
                  className="flex justify-center items-center   w-full max-w-[56px] h-full max-h-[56px] border-2 cursor-pointer border-solid border-[#EDEDED]"
                >
                  {/* <Image src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${file?.url}`} alt="product Image" width={56} height={56} objectFit="cover" /> */}
                  <img src={`${process.env.NEXT_PUBLIC_BACKEND_URL}/${file?.url}`} alt="product Image" width={56} height={56} className="object-cover" />
                </div>
              );
            })}
          </Slider>
        </div>
      </div>
    </Modal>
  );
}

export default ProductGalleryModal;
