import React from 'react';
import Title from '../../components/Shared/Title/Title';
import Paragraph from '../../components/Shared/Paragraph/Paragraph';
import { Metadata } from 'next';
import { Empty } from 'antd';

export const metadata: Metadata = {
  title: {
    absolute: 'Orion Schaltgeraete',
  },
  description: 'Orion Schaltgeraete',
  authors: [{ name: 'Orion Schaltgeraete' }],
  keywords: ['Orion Schaltgeraete'],
};

async function getData() {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/datenschutz`,
    {
      // next: { revalidate: 1 },
      cache: 'no-cache',
    }
  );

  if (!res.ok) {
    throw new Error('Failed to fetch data');
  }

  return res.json();
}

interface searchParamsTypes {
  params: { locale: string };
}

const GDPR = async ({ params }: searchParamsTypes) => {
  const { locale } = params;
  const data = await getData();

  return (
    <>
      {data?.Datenschutzs?.length === 0 ? (
        <Empty className="py-10" />
      ) : (
        <div className="pb-16 tablet:pb-12 mobile:!pb-10">
          <Title
            text={`${
              locale === 'en'
                ? data?.Datenschutzs[0]?.title
                : locale === 'de'
                ? data?.Datenschutzs[0]?.titleGe
                : locale === 'fr'
                ? data?.Datenschutzs[0]?.titleFr
                : ''
            }`}
          />
          <div className="flex flex-col gap-y-4 mx-auto max-w-[742px] px-6 pb-6">
            <Paragraph
              text={`${
                locale === 'en'
                  ? data?.Datenschutzs[0]?.description
                  : locale === 'de'
                  ? data?.Datenschutzs[0]?.descriptionGe
                  : locale === 'fr'
                  ? data?.Datenschutzs[0]?.descriptionFr
                  : ''
              }`}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default GDPR;
