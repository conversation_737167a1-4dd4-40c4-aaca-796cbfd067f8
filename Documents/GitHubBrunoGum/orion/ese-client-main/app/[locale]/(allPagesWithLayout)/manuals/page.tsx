import React from 'react';
import ProductCard from '../../components/Shared/ProductCard/ProductCard';
import { Col, Row, Pagination } from 'antd';
import { useTranslations } from 'next-intl';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: {
    absolute: 'Orion Schaltgeraete',
  },
  description: 'Orion Schaltgeraete',
  authors: [{ name: 'Orion Schaltgeraete' }],
  keywords: ['Orion Schaltgeraete'],
};

const Page = () => {
  const t = useTranslations('Manuals');

  const prodCard = useTranslations('ProductCard');
  const cardTitle = prodCard('title');
  const cardDesc = prodCard('description');
  const cardBtn = prodCard('btn_text');

  const products = new Array(15).fill(null);
  return (
    <div className="flexCenter flex-col px-12 mobilelg:px-5">
      <h1 className="text-[60px] mobile:text-[30px] font-bold text-gray-800 py-9">
        {t('title')}
      </h1>
      <Row className=" tabletlg:px-0">
        {products?.map((_, index) => (
          <Col
            className="flexCenter p-0 pb-9"
            key={index}
            xxl={8}
            xl={8}
            lg={8}
            md={8}
            sm={12}
            xs={12}
          >
            <ProductCard
              title={cardTitle}
              description={cardDesc}
              btnText={cardBtn}
            />
          </Col>
        ))}
      </Row>
      <Pagination
        defaultCurrent={1}
        total={50}
        className="pb-[4rem] font-bold"
      />
    </div>
  );
};
export default Page;
