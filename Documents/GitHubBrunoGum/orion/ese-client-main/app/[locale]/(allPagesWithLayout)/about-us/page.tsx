import React from 'react';
import Title from '../../components/Shared/Title/Title';
import Paragraph from '../../components/Shared/Paragraph/Paragraph';
import { Metadata } from 'next';
import { Empty } from 'antd';

export const metadata: Metadata = {
  title: {
    absolute: 'Orion Schaltgeraete',
  },
  description: 'Orion Schaltgeraete',
  authors: [{ name: 'Orion Schaltgeraete' }],
  keywords: ['Orion Schaltgeraete'],
};

async function getData() {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BACKEND_URL}/unternehmen`,
    {
      // next: { revalidate: 1 },
      cache: 'no-cache',
    }
  );

  if (!res.ok) {
    throw new Error('Failed to fetch data');
  }

  return res.json();
}

interface searchParamsTypes {
  params: { locale: string };
}

const AboutUs = async ({ params }: searchParamsTypes) => {
  const { locale } = params;
  const data = await getData();

  return (
    <div>
      {data?.Unternehmens?.length === 0 ? (
        <Empty className="py-10" />
      ) : (
        <>
          <Title
            text={`${
              locale === 'en'
                ? data?.Unternehmens[0]?.title
                : locale === 'de'
                ? data?.Unternehmens[0]?.titleGe
                : locale === 'fr'
                ? data?.Unternehmens[0]?.titleFr
                : ''
            }`}
          />
          <div className="mx-auto text-center flex-col max-w-[723px] px-4 pb-10">
            <Paragraph
              text={`${
                locale === 'en'
                  ? data?.Unternehmens[0]?.description
                  : locale === 'de'
                  ? data?.Unternehmens[0]?.descriptionGe
                  : locale === 'fr'
                  ? data?.Unternehmens[0]?.descriptionFr
                  : ''
              }`}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default AboutUs;
