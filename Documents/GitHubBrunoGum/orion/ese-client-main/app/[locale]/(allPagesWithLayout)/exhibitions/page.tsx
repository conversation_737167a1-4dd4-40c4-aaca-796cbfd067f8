import React from "react";
import { Metadata } from "next";
import { Empty } from "antd";
import Title from "../../components/Shared/Title/Title";
import Paragraph from "../../components/Shared/Paragraph/Paragraph";

export const metadata: Metadata = {
  title: {
    absolute: "Orion Schaltgeraete",
  },
  description: "Orion Schaltgeraete",
  authors: [{ name: "Orion Schaltgeraete" }],
  keywords: ["Orion Schaltgeraete"],
};

async function getData() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/messe`, {
    // next: { revalidate: 1 },
    cache: "no-cache",
  });

  if (!res.ok) {
    throw new Error("Failed to fetch data");
  }

  return res.json();
}

interface searchParamsTypes {
  params: { locale: string };
}

const Messe = async ({ params }: searchParamsTypes) => {
  const { locale } = params;
  const data = await getData();

  return (
    <div>
      {data?.manuals?.length === 0 ? (
        <Empty className="py-10" />
      ) : (
        <>
          <Title
            text={`${
              locale === "en" ? data?.manuals[0]?.title : locale === "de" ? data?.manuals[0]?.titleGe : locale === "fr" ? data?.manuals[0]?.titleFr : ""
            }`}
          />
          <div className="mx-auto text-center flex-col w-fit max-w-[723px] px-4 pb-10">
            <Paragraph
              text={`${
                locale === "en"
                  ? data?.manuals[0]?.description
                  : locale === "de"
                    ? data?.manuals[0]?.descriptionGe
                    : locale === "fr"
                      ? data?.manuals[0]?.descriptionFr
                      : ""
              }`}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default Messe;
