import React from 'react';
import HomeBanner from '../components/HomeBanner/Homebanner';
import ProductsOverview from '../components/Products/Products';
import BuyFromUs from '../components/BuyFromUs/BuyFromUs';
import { useTranslations } from 'next-intl';
import { Metadata } from 'next';
interface compTypes {
  params: { locale: string };
  searchParams: { categPage: number };
}

export const metadata: Metadata = {
  title: {
    absolute: 'Orion Schaltgeraete',
  },
  description: 'Orion Schaltgeraete',
  authors: [{ name: 'Orion Schaltgeraete' }],
  keywords: ['Orion Schaltgeraete'],
};

const Home = ({ params, searchParams }: compTypes) => {
  const { locale } = params;
  const { categPage } = searchParams;
  const t = useTranslations('Home');
  const title = t('product_overview.main_title');

  return (
    <div>
      <HomeBanner />
      <ProductsOverview mainTitle={title} locale={locale} page={categPage} />
      <BuyFromUs />
    </div>
  );
};

export default Home;
