import { useTranslations } from 'next-intl';
import React from 'react';
import Title from '../../components/Shared/Title/Title';
import DownLoads from '../../components/DownLoads/DownLoads';
import { Metadata } from 'next';
interface searchParamsTypes {
  page: number;
}

export const metadata: Metadata = {
  title: {
    absolute: 'Orion Schaltgeraete',
  },
  description: 'Orion Schaltgeraete',
  authors: [{ name: 'Orion Schaltgeraete' }],
  keywords: ['Orion Schaltgeraete'],
};

function Page({
  searchParams,
  params,
}: {
  searchParams: searchParamsTypes;
  params: any;
}) {
  const { page } = searchParams;
  const { locale } = params;
  const t = useTranslations('Downloads');
  return (
    <div className="flexCenter w-full flex-col ">
      <Title text={t('main_title')} />
      <div className="flex justify-center items-center w-full  flex-col px-[40px] tablet:px-[20px]">
        <DownLoads page={page} locale={locale} />
      </div>
    </div>
  );
}

export default Page;
