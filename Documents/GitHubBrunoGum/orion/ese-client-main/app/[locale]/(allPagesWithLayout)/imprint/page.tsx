import React from 'react';
import Title from '../../components/Shared/Title/Title';
import { useTranslations } from 'next-intl';
import ImprintText from '../../components/Imprint/Imprint';

interface paramsTypes {
  locale: string;
}

export const metadata = {
  title: {
    absolute: 'Orion Schaltgeraete',
  },
  description: 'Orion Schaltgeraete',
  authors: [{ name: 'Orion Schaltgeraete' }],
  keywords: ['Orion Schaltgeraete'],
};

const Page = ({ params }: { params: paramsTypes }) => {
  const t = useTranslations('Imprint');
  const { locale } = params;
  return (
    <div>
      <Title text={t('title')} />
      <ImprintText locale={locale} />
    </div>
  );
};

export default Page;
