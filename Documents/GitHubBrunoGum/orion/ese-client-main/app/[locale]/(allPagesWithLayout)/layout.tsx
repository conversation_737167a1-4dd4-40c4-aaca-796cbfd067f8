import BackToTop from '../components/Shared/BackToTop/BackToTop';
import Footer from '../components/Shared/Layout/Footer/Footer';
import Header from '../components/Shared/Layout/Header/Header';

export default function RootLayout({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  return (
    <div className="w-full flexCenter">
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1"
        />
      </head>
      <div className="w-full flex flex-col">
        <Header locale={locale} />
        <section className="w-full">{children}</section>
        <Footer locale={locale} />
        <BackToTop />
      </div>
    </div>
  );
}
