import { Col, Pagination, Row } from 'antd';
import React from 'react';
// import Sidebar from '../../../components/Shared/Product/Sidebar';
import { useTranslations } from 'next-intl';
// import ProductByCat from '../../../components/Shared/Product/ProductByCat';
// import ProductsOverview from '../../../components/Products/Products';
// import SidebarMenu from '../../../components/Shared/Product/SidebarMenu';
import { Metadata } from 'next';
import ProductsOverview from '@/app/[locale]/components/Products/Products';
import ProductByCat from '@/app/[locale]/components/Shared/Product/ProductByCat';
interface searchParamsTypes {
  categoryId: number;
  page: number;
  categPage: number;
  sidebarPage?: number;
  subCategoryId?: number;
  bigCategoryId?: number;
}

export const metadata: Metadata = {
  title: {
    absolute: 'Orion Schaltgeraete',
  },
  description: 'Orion Schaltgeraete',
  authors: [{ name: 'Orion Schaltgeraete' }],
  keywords: ['Orion Schaltgeraete'],
};

const ProdByCateg = ({
  searchParams,
  params,
}: {
  searchParams: searchParamsTypes;
  params: { locale: string; categoryId: number };
}) => {
  const {
    // categoryId,
    page,
    categPage,
    sidebarPage,
    subCategoryId,
    bigCategoryId,
  } = searchParams;
  const { locale, categoryId } = params;
console.log("categoryId",categoryId)
  const categories = useTranslations('Categories');
  const catgTitle = categories('title');
  const Products = useTranslations('Products');
  const title = Products('title');

  return (
    <div className="w-full">
      <Row className="flex justify-center">
        
        <Col
          xxl={24}
          xl={24}
          lg={24}
          md={24}
          sm={24}
          xs={24}
          className="mobile:px-4 px-6"
        >
          <ProductByCat
            title={title}
            catgTitle={catgTitle}
            categoryId={+categoryId}
            page={page}
            categPage={categPage}
            locale={locale}
            sidebarPage={sidebarPage}
            subCategoryId={subCategoryId}
            bigCategoryId={bigCategoryId}
          />
        </Col>
      </Row>
    </div>
  );
};

export default ProdByCateg;
