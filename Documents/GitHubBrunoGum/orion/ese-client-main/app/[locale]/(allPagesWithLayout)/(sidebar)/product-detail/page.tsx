import React from 'react';
import ProductDetail from '../../../components/ProductDetail/ProductDetail';
import { useTranslations } from 'next-intl'; 

interface searchParamsTypes {
  searchParams: { productId: number; sidebarPage: number };
  params: { locale: string };
}

function Page({ searchParams, params }: searchParamsTypes) {
  const { productId, sidebarPage } = searchParams;
  const { locale } = params;
  const categories = useTranslations('Categories');
  const catgTitle = categories('title');
  const Products = useTranslations('Products');
  const prodTitle = Products('title');
  const t = useTranslations('Product_Detail');

  return (
    <div className="w-full"> 
      <ProductDetail
        productId={productId}
        catgTitle={catgTitle}
        locale={locale}
        prodTitle={prodTitle}
        sidebarPage={sidebarPage}
        contactText={t('contact_btn_text')}
        downloadText={t('btn_text')} 
      />
    </div>
  );
}

export default Page;
