// 'use client';
import { Col, Row } from "antd";
import localFont from "next/font/local";

import Sidebar from "../../components/Shared/Product/Sidebar";
// import SidebarMenu from '../../components/Shared/Product/SidebarMenu';
import { useSearchParams } from "next/navigation";
import dynamic from "next/dynamic";
import { useTranslations } from "next-intl";
const SidebarMenu = dynamic(() => import("../../components/Shared/Product/SidebarMenu"), { ssr: false });

const heebo = localFont({
  src: [
    {
      path: "../../../../public/assets/fonts/heebo.woff2",
      weight: "400",
    },
  ],
  variable: "--font-heebo",
});

export default function RootLayout({
  children,
  params: { locale },
}: // searchParams,
{
  children: React.ReactNode;
  params: { locale: string };
}) {
  const categories = useTranslations("Categories");
  const catgTitle = categories("title");
  return (
    <div className={`w-full flexCenter ${heebo.variable}`}>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
      </head>
      <Row className="w-full">
        {" "}
        <Col
          xxl={5}
          xl={7}
          lg={7}
          md={7}
          sm={0}
          xs={0}
          className="tablet:hidden pt-[4rem] bg-white max-h-[calc(100dvh-170px)] overflow-y-auto border-r-[1px] menuLeft"
        >
          <Sidebar catgTitle={catgTitle}>
            <SidebarMenu
              // categPage={categPage}
              // prodPage={page}
              // page={sidebarPage}
              // categoryId={categoryId}
              locale={locale}
            />
          </Sidebar>
        </Col>
        <Col xxl={19} xl={17} lg={17} md={17} sm={24} xs={24} className="pb-32">
          {children}
        </Col>
      </Row>
    </div>
  );
}
