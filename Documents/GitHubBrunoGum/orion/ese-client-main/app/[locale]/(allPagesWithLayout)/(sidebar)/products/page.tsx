import React from "react";
import { useTranslations } from "next-intl";
import ProductComp from "../../../components/ProductsComp/ProductComp";
interface compTypes {
  params: { locale: string };
  searchParams: { categPage: number; sidebarPage?: number; page: number };
}

const Page = ({ params, searchParams }: compTypes) => {
  const { locale } = params;
  const { categPage, sidebarPage, page } = searchParams;
  const categories = useTranslations("Categories");
  const catgTitle = categories("title");

  return (
    <div className="w-full">
      <ProductComp catgTitle={catgTitle} locale={locale} page={categPage} sidebarPage={sidebarPage} pagination={page} />
    </div>
  );
};

export default Page;
