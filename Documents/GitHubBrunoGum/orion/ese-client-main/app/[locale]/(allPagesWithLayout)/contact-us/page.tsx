import { Col, Image, Row } from "antd";
import React from "react";
import Map from "../../components/Map/Map";
import { useTranslations } from "next-intl";
import Title from "../../components/Shared/Title/Title";
import Paragraph from "../../components/Shared/Paragraph/Paragraph";
import ContactForm from "../../components/ContactForm/ContactForm";
import ContactNumber from "../../components/ContactNumber";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: {
    absolute: "Orion Schaltgeraete",
  },
  description: "Orion Schaltgeraete",
  authors: [{ name: "<PERSON> Schaltgeraete" }],
  keywords: ["Orion Schaltgeraete"],
};

const ContactUs = ({ params }: { params: { locale: string } }) => {
  const t = useTranslations("contact_us");
  const { locale } = params;

  return (
    <Row gutter={[32, 8]} className="px-12 py-14 mobile:py-2 mobile:px-2 tabletlg:flex-col-reverse w-full">
      <Col xl={10} lg={10} md={24} sm={24} xs={24}>
        <div className="block tabletlg:hidden">
          <div className="flex justify-start my-[-60px]">
            <Title text={t("title")} />
          </div>
          <Paragraph text={t("subTitle")} />
        </div>
        <div className="pt-12 tabletlg:pr-0">
          <ContactForm
            name={t("input_name")}
            nameAlert={t("messages.name")}
            email={t("input_mail")}
            phone={t("input_phone")}
            emailAlert={t("messages.email")}
            checkBoxAlert={t("messages.checkbox")}
            phoneAlert={t("messages.phone")}
            message={t("input_message")}
            checkboxText={t("checkBox.text")}
            checkboxLink={t("checkBox.link.text")}
            checkboxUrl={t("checkBox.link.url")}
            checkboxSuffix={t("checkBox.suffix")}
            submit={t("buttonText")}
            locale={locale}
          />
        </div>
        <div className="hidden tabletlg:block tabletlg:pb-5">
          <div className="flex flex-col gap-y-8 max-w-[735px] pt-10">
            <div className="flex flex-row gap-x-[15px]">
              <div className="flexCenter">
                <img src="/assets/icons/call-black.svg" alt="Call Icon" />
              </div>
              <div>
                <div>
                  <p className="font-normal text-[13px]">{t("contact.title")}</p>
                  <ContactNumber />
                </div>
              </div>
            </div>
            <div className="flex flex-row gap-x-[15px]">
              <div className="flexCenter">
                <img src="/assets/icons/mail-black.svg" alt="Mail Icon" />
              </div>
              <div>
                <div>
                  <p className="font-normal text-[13px]">{t("mail.title")}</p>
                  <a href="mailto:<EMAIL>" className="font-normal text-[13px] text-secondry_color">
                    {t("mail.data")}
                  </a>
                </div>
              </div>
            </div>
            <div className="flex flex-row gap-x-[15px]">
              <div className="flexCenter">
                <img src="/assets/icons/distance-black.svg" alt="Location Icon" />
              </div>
              <div>
                <p className="font-normal text-[13px]">{t("address.title")}</p>
                <a
                  href={
                    "https://www.google.com/maps/place/Donatusstr.+26A,+50767+Köln,+Germany/@51.0067568,6.8673614,17z/data=!3m1!4b1!4m6!3m5!1s0x47bf304523d5b2d7:0x1fd3934f6bd88ae6!8m2!3d51.0067568!4d6.8699363!16s%2Fg%2F11bw421htz?entry=ttu"
                  }
                  target="_blank"
                  className="font-normal text-[13px] text-secondry_color"
                >
                  {t("address.data")}
                </a>
              </div>
            </div>
          </div>
        </div>
      </Col>
      <Col xl={14} lg={14} md={24} sm={24} xs={24}>
        <div className="tabletlg:flex tabletlg:flex-col">
          <div className="hidden tabletlg:block py-4">
            <div className="flexCenter flex-col">
              <div className="flex justify-start my-[-20px]">
                <Title text={t("title")} />
              </div>
              <Paragraph text={t("subTitle")} />
            </div>
          </div>
          {/* <Map /> */}
          <iframe
            className="max-w-[900px] h-[640px] tabletlg:h-[450px]"
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2510.506045301524!2d6.870078299999999!3d51.0067994!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47bf304523d5b2d7%3A0x1fd3934f6bd88ae6!2sDonatusstr.%2026A%2C%2050767%20K%C3%B6ln%2C%20Germany!5e0!3m2!1sen!2snl!4v1741356151576!5m2!1sen!2snl"
            width="100%"
            // height="100%"
            style={{ border: 0 }}
            // allowfullscreen=""
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
          ></iframe>
        </div>
        <div className="block tabletlg:hidden">
          <div className="flex justify-between gap-x-2 max-w-[735px] pt-10">
            <div className="flex flex-row gap-x-[15px]">
              <div className="flexCenter">
                <img src="/assets/icons/call-black.svg" alt="Call Icon" />
              </div>
              <div>
                <div>
                  <p className="font-normal text-[13px]">{t("contact.title")}</p>
                  <ContactNumber />
                </div>
              </div>
            </div>
            <div className="flex flex-row gap-x-[15px]">
              <div className="flexCenter">
                <img src="/assets/icons/mail-black.svg" alt="Mail Icon" />
              </div>
              <div>
                <div>
                  <p className="font-normal text-[13px]">{t("mail.title")}</p>
                  <a href="mailto:<EMAIL>" className="font-normal text-[13px] text-secondry_color">
                    {t("mail.data")}
                  </a>
                </div>
              </div>
            </div>
            <div className="flex flex-row gap-x-[15px]">
              <div className="flexCenter">
                <img src="/assets/icons/distance-black.svg" alt="Location Icon" />
              </div>
              <div>
                <p className="font-normal text-[13px]">{t("address.title")}</p>
                <a
                  href={
                    "https://www.google.com/maps/place/Donatusstr.+26A,+50767+Köln,+Germany/@51.0067568,6.8673614,17z/data=!3m1!4b1!4m6!3m5!1s0x47bf304523d5b2d7:0x1fd3934f6bd88ae6!8m2!3d51.0067568!4d6.8699363!16s%2Fg%2F11bw421htz?entry=ttu"
                  }
                  target="_blank"
                  className="font-normal text-[13px] text-secondry_color"
                >
                  {t("address.data")}
                </a>
              </div>
            </div>
          </div>
        </div>
      </Col>
    </Row>
  );
};

export default ContactUs;
