import React from 'react';
import Title from '../../components/Shared/Title/Title';
import { Col, Row } from 'antd';
import Product from '../../components/Shared/Product/Product';
import {
  ProductList,
  ProductType,
} from '../../components/Products/ProductList.data';
import Sidebar from '../../components/Shared/Product/Sidebar';
import { useTranslations } from 'next-intl';
import SidebarMenu from '../../components/Shared/Product/SidebarMenu';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: {
    absolute: 'Orion Schaltgeraete',
  },
  description: 'Orion Schaltgeraete',
  authors: [{ name: 'Orion Schaltgeraete' }],
  keywords: ['Orion Schaltgeraete'],
};

const Categories = () => {
  const categories = useTranslations('Categories');
  const catgTitle = categories('title');

  return (
    <Row>
      <Col
        xxl={6}
        xl={6}
        lg={6}
        md={6}
        sm={0}
        xs={0}
        className="tablet:hidden pt-16 border-r-[1px] menuLeft"
      >
        {/* <Sidebar catgTitle={catgTitle}>
          <SidebarMenu />
        </Sidebar> */}
      </Col>
      <Col xxl={18} xl={18} lg={18} md={18} sm={24} xs={24} className="px-4">
        <div className="flexCenter flex-col">
          <Title text={categories('title')} />
          <div className="hidden tablet:flex justify-center pb-3 pt-6 border-2 bg-[#1A75CF] txtWhite w-full">
            {/* <Sidebar catgTitle={catgTitle}>
              <SidebarMenu />
            </Sidebar> */}
          </div>
        </div>
        <Row gutter={[12, 12]} className=" flexCenter py-10">
          {ProductList?.map((product: ProductType) => (
            <Col key={product?.id} className="">
              <Product img={product?.img} text={product?.text} />
            </Col>
          ))}
        </Row>
      </Col>
    </Row>
  );
};

export default Categories;
