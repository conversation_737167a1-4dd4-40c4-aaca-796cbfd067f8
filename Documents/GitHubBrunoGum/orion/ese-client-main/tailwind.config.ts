// import type { Config } from 'tailwindcss';

// const config: Config = {
//   content: [
//     './app/**/*.{js,ts,jsx,tsx}',
//     './pages/**/*.{js,ts,jsx,tsx}',
//     './components/**/*.{js,ts,jsx,tsx}',
//     './layout/**/*.{js,ts,jsx,tsx}',
//     '!./node_modules',
//   ],
//   theme: {
//     extend: {
//       colors: {
//         primary_color: '#012A4D',
//         secondry_color: '#006EB8',
//       },
//     },
//     screens: {
//       mobile: { max: '575px' },
//       mobilelg: { max: '650px' },
//       tablet: { max: '768px' },
//       tabletlg: { max: '992px' },
//       desktop: { max: '1440px' },
//       minTabletlg: { min: '1200px' },
//     },
//   },
//   // corePlugins: {
//   //    preflight: false, // Disable the default styles applied by Tailwind CSS
//   // },
//   plugins: [],
// };

// export default config;

import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './app/**/*.{js,ts,jsx,tsx}',
    './pages/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './layout/**/*.{js,ts,jsx,tsx}',
    '!./node_modules',
  ],
  theme: {
    extend: {
      colors: {
        primary_color: '#012A4D',
        secondry_color: '#006EB8',
      },
      fontFamily: {
        heebo: ["var(--font-heebo)"]
      }
    },
    screens: {
      mobile: { max: '575px' },
      mobilelg: { max: '650px' },
      tablet: { max: '768px' },
      tabletlg: { max: '992px' },
      desktop: { max: '1440px' },
      minTabletlg: { min: '1200px' },
    },
  },
  plugins: [
    function ({ addBase }: { addBase: any }) {
      addBase({
        h1: {
          fontSize: '2.25rem', // You can adjust this value
          fontWeight: '700',
          lineHeight: '2.5rem',
        },
        h2: {
          fontSize: '1.875rem', // You can adjust this value
          fontWeight: '600',
          lineHeight: '2.25rem',
        },
        h3: {
          fontSize: '1.5rem', // You can adjust this value
          fontWeight: '500',
          lineHeight: '2rem',
        },
        ul: {
          paddingLeft: '1.5rem', // Adjust padding as needed
          listStyleType: 'disc',
        },
        li: {
          marginBottom: '0.5rem', // Adjust margin as needed
        },
        ol: {
          paddingLeft: '1.5rem', // Adjust padding as needed
          listStyleType: 'decimal',
        },
      });
    },
  ],
};

export default config;
